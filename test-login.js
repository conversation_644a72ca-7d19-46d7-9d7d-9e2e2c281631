// Test login endpoint
const http = require('http');

console.log('🧪 Testing Wistron Login API...\n');

function testLogin(email, password, expectedStatus = 200) {
  return new Promise((resolve, reject) => {
    const loginData = JSON.stringify({
      email: email,
      password: password
    });

    console.log(`Testing login for: ${email}`);
    console.log(`Request data: ${loginData}`);

    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log(`Response status: ${res.statusCode}`);
        console.log(`Response data: ${data}`);
        
        try {
          const response = JSON.parse(data);
          if (res.statusCode === expectedStatus) {
            console.log(`✅ Test passed for ${email}`);
            if (response.success) {
              console.log(`   User: ${response.data.user.email}`);
              console.log(`   Role: ${response.data.user.role}`);
            }
          } else {
            console.log(`❌ Test failed for ${email} - Expected ${expectedStatus}, got ${res.statusCode}`);
          }
        } catch (e) {
          console.log(`❌ JSON parse error: ${e.message}`);
        }
        
        console.log('---');
        resolve();
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Request error for ${email}: ${err.message}`);
      reject(err);
    });

    req.write(loginData);
    req.end();
  });
}

async function runTests() {
  try {
    // Test 1: Valid admin login
    await testLogin('<EMAIL>', 'Admin@123456', 200);
    
    // Test 2: Valid agent login
    await testLogin('<EMAIL>', 'Agent@123456', 200);
    
    // Test 3: Invalid credentials
    await testLogin('<EMAIL>', 'wrongpass', 401);
    
    // Test 4: Test routes endpoint
    console.log('Testing routes endpoint...');
    const routesReq = http.request({
      hostname: 'localhost',
      port: 5000,
      path: '/api/v1/routes',
      method: 'GET'
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('✅ Routes endpoint working');
          console.log('Available routes:');
          response.data.routes.forEach(route => {
            console.log(`   ${route.method} ${route.path} - ${route.description}`);
          });
        } catch (e) {
          console.log('❌ Routes endpoint error:', e.message);
        }
        
        showSummary();
      });
    });
    
    routesReq.on('error', (err) => {
      console.log('❌ Routes request error:', err.message);
    });
    
    routesReq.end();
    
  } catch (error) {
    console.log('❌ Test suite error:', error.message);
  }
}

function showSummary() {
  console.log('\n🎉 ========================================');
  console.log('   Wistron Money Transfer - Test Summary');
  console.log('   نظام ويسترون - ملخص الاختبارات');
  console.log('========================================');
  console.log('✅ اختبارات API مكتملة!');
  console.log('');
  console.log('🌐 النظام متاح على:');
  console.log('   الصفحة الرئيسية: http://localhost:5000');
  console.log('   فحص الصحة: http://localhost:5000/health');
  console.log('   قائمة المسارات: http://localhost:5000/api/v1/routes');
  console.log('');
  console.log('👤 حسابات التجربة:');
  console.log('   المدير: <EMAIL> / Admin@123456');
  console.log('   الوكيل: <EMAIL> / Agent@123456');
  console.log('');
  console.log('🔧 مسارات API المتاحة:');
  console.log('   POST /api/v1/auth/login - تسجيل الدخول');
  console.log('   POST /api/v1/auth/register - إنشاء حساب');
  console.log('   GET  /api/v1/rates - أسعار الصرف');
  console.log('   GET  /api/v1/dashboard/stats - الإحصائيات');
  console.log('   GET  /api/v1/routes - قائمة المسارات');
  console.log('========================================');
}

// Start tests
runTests();
