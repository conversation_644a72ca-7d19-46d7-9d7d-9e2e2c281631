/**
 * Global Error Handler Middleware
 * وسطاء معالجة الأخطاء العام
 */

const logger = require('../utils/logger');
const {
  AppError,
  createErrorResponse,
  isOperationalError,
  ERROR_CODES
} = require('../utils/errors');

/**
 * Main error handler middleware
 */
const errorHandler = (error, req, res, next) => {
  let formattedError = error;

  // Log the original error
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString()
  });

  // Handle specific error types
  if (!isOperationalError(error)) {
    // Database errors
    if (error.code && (error.code.startsWith('23') || error.code.startsWith('08') || error.code === '40001')) {
      formattedError = handleDatabaseError(error);
    }
    // JWT errors
    else if (error.name && error.name.includes('Token')) {
      formattedError = handleJWTError(error);
    }
    // Validation errors
    else if (error.isJoi || (error.array && typeof error.array === 'function')) {
      formattedError = handleValidationError(error);
    }
    // Generic programming errors
    else {
      formattedError = new AppError(
        process.env.NODE_ENV === 'production'
          ? 'Internal server error'
          : error.message,
        500,
        'INTERNAL_SERVER_ERROR',
        process.env.NODE_ENV === 'development' ? { originalError: error.message } : null
      );
    }
  }

  // Get language preference from request headers
  const language = req.headers['accept-language']?.includes('ar') ? 'ar' : 'en';

  // Create error response
  const errorResponse = createErrorResponse(formattedError, language);

  // Log security-related errors
  if (formattedError.statusCode === 401 || formattedError.statusCode === 403) {
    logger.security('Security error occurred', {
      error: formattedError.message,
      code: formattedError.code,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
      method: req.method,
      userId: req.user?.id
    });
  }

  // Send error response
  res.status(formattedError.statusCode || 500).json(errorResponse);
};

/**
 * Handle Knex/PostgreSQL database errors
 * @param {Error} error - Database error
 * @returns {AppError} Formatted application error
 */
const handleDatabaseError = (error) => {
  logger.database('Database error occurred', { error: error.message, code: error.code });

  // PostgreSQL error codes
  switch (error.code) {
    case '23505': // Unique violation
      return new AppError(
        'Resource already exists',
        409,
        ERROR_CODES.DUPLICATE_TRANSACTION,
        { constraint: error.constraint }
      );

    case '23503': // Foreign key violation
      return new AppError(
        'Referenced resource not found',
        400,
        ERROR_CODES.RECIPIENT_NOT_FOUND,
        { constraint: error.constraint }
      );

    case '23502': // Not null violation
      return new AppError(
        'Required field is missing',
        400,
        ERROR_CODES.REQUIRED_FIELD_MISSING,
        { column: error.column }
      );

    default:
      return new AppError(
        'Database operation failed',
        500,
        ERROR_CODES.DATABASE_CONNECTION_ERROR,
        { originalError: error.message }
      );
  }
};

/**
 * Handle JWT errors
 * @param {Error} error - JWT error
 * @returns {AppError} Formatted application error
 */
const handleJWTError = (error) => {
  logger.security('JWT error occurred', { error: error.message });

  if (error.name === 'TokenExpiredError') {
    return new AppError(
      'Token has expired',
      401,
      ERROR_CODES.TOKEN_EXPIRED
    );
  }

  if (error.name === 'JsonWebTokenError') {
    return new AppError(
      'Invalid token',
      401,
      ERROR_CODES.TOKEN_INVALID
    );
  }

  return new AppError(
    'Authentication failed',
    401,
    ERROR_CODES.TOKEN_INVALID
  );
};

/**
 * Handle validation errors
 * @param {Error} error - Validation error
 * @returns {AppError} Formatted application error
 */
const handleValidationError = (error) => {
  logger.warn('Validation error occurred', { error: error.message });

  // Joi validation errors
  if (error.isJoi) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));

    return new AppError(
      'Validation failed',
      400,
      ERROR_CODES.INVALID_FORMAT,
      { validationErrors: details }
    );
  }

  return new AppError(
    'Validation failed',
    400,
    ERROR_CODES.INVALID_FORMAT
  );
};

/**
 * Async error wrapper
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Wrapped function
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  errorHandler,
  asyncHandler,
  handleDatabaseError,
  handleJWTError,
  handleValidationError
};
  logger.database('Database error occurred', { error: error.message, code: error.code });

  // PostgreSQL error codes
  switch (error.code) {
    case '23505': // Unique violation
      return new AppError(
        'Resource already exists',
        409,
        ERROR_CODES.DUPLICATE_TRANSACTION,
        { constraint: error.constraint }
      );
    
    case '23503': // Foreign key violation
      return new AppError(
        'Referenced resource not found',
        400,
        ERROR_CODES.RECIPIENT_NOT_FOUND,
        { constraint: error.constraint }
      );
    
    case '23502': // Not null violation
      return new AppError(
        'Required field is missing',
        400,
        ERROR_CODES.REQUIRED_FIELD_MISSING,
        { column: error.column }
      );
    
    case '23514': // Check violation
      return new AppError(
        'Invalid data format',
        400,
        ERROR_CODES.INVALID_FORMAT,
        { constraint: error.constraint }
      );
    
    case '08006': // Connection failure
    case '08001': // Unable to connect
      return new AppError(
        'Database connection error',
        503,
        ERROR_CODES.DATABASE_CONNECTION_ERROR
      );
    
    case '40001': // Serialization failure
      return new AppError(
        'Transaction conflict, please retry',
        409,
        ERROR_CODES.DUPLICATE_TRANSACTION
      );
    
    default:
      return new AppError(
        'Database operation failed',
        500,
        ERROR_CODES.DATABASE_CONNECTION_ERROR,
        { originalError: error.message }
      );
  }
};

/**
 * Handle JWT errors
 * @param {Error} error - JWT error
 * @returns {AppError} Formatted application error
 */
const handleJWTError = (error) => {
  logger.security('JWT error occurred', { error: error.message });

  if (error.name === 'TokenExpiredError') {
    return new AppError(
      'Token has expired',
      401,
      ERROR_CODES.TOKEN_EXPIRED
    );
  }
  
  if (error.name === 'JsonWebTokenError') {
    return new AppError(
      'Invalid token',
      401,
      ERROR_CODES.TOKEN_INVALID
    );
  }
  
  if (error.name === 'NotBeforeError') {
    return new AppError(
      'Token not active yet',
      401,
      ERROR_CODES.TOKEN_INVALID
    );
  }
  
  return new AppError(
    'Authentication failed',
    401,
    ERROR_CODES.TOKEN_INVALID
  );
};

/**
 * Handle validation errors
 * @param {Error} error - Validation error
 * @returns {AppError} Formatted application error
 */
const handleValidationError = (error) => {
  logger.warn('Validation error occurred', { error: error.message });

  // Express-validator errors
  if (error.array && typeof error.array === 'function') {
    const errors = error.array();
    return new AppError(
      'Validation failed',
      400,
      ERROR_CODES.INVALID_FORMAT,
      { validationErrors: errors }
    );
  }

  // Joi validation errors
  if (error.isJoi) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));
    
    return new AppError(
      'Validation failed',
      400,
      ERROR_CODES.INVALID_FORMAT,
      { validationErrors: details }
    );
  }

  return new AppError(
    'Validation failed',
    400,
    ERROR_CODES.INVALID_FORMAT
  );
};

/**
 * Handle Multer file upload errors
 * @param {Error} error - Multer error
 * @returns {AppError} Formatted application error
 */
const handleMulterError = (error) => {
  logger.warn('File upload error occurred', { error: error.message, code: error.code });

  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      return new AppError(
        'File size too large',
        413,
        ERROR_CODES.FILE_TOO_LARGE,
        { maxSize: error.field }
      );
    
    case 'LIMIT_FILE_COUNT':
      return new AppError(
        'Too many files uploaded',
        413,
        ERROR_CODES.FILE_UPLOAD_FAILED,
        { maxCount: error.field }
      );
    
    case 'LIMIT_UNEXPECTED_FILE':
      return new AppError(
        'Unexpected file field',
        400,
        ERROR_CODES.INVALID_FILE_TYPE,
        { fieldName: error.field }
      );
    
    default:
      return new AppError(
        'File upload failed',
        400,
        ERROR_CODES.FILE_UPLOAD_FAILED
      );
  }
};

/**
 * Handle Redis errors
 * @param {Error} error - Redis error
 * @returns {AppError} Formatted application error
 */
const handleRedisError = (error) => {
  logger.error('Redis error occurred', { error: error.message });
  
  return new AppError(
    'Cache service unavailable',
    503,
    ERROR_CODES.REDIS_CONNECTION_ERROR,
    { originalError: error.message }
  );
};

/**
 * Main error handler middleware
 */
const errorHandler = (error, req, res, next) => {
  let formattedError = error;

  // Log the original error
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString()
  });

  // Handle specific error types
  if (!isOperationalError(error)) {
    // Database errors
    if (error.code && (error.code.startsWith('23') || error.code.startsWith('08') || error.code === '40001')) {
      formattedError = handleDatabaseError(error);
    }
    // JWT errors
    else if (error.name && error.name.includes('Token')) {
      formattedError = handleJWTError(error);
    }
    // Validation errors
    else if (error.isJoi || (error.array && typeof error.array === 'function')) {
      formattedError = handleValidationError(error);
    }
    // Multer errors
    else if (error.code && error.code.startsWith('LIMIT_')) {
      formattedError = handleMulterError(error);
    }
    // Redis errors
    else if (error.message && error.message.includes('Redis')) {
      formattedError = handleRedisError(error);
    }
    // Generic programming errors
    else {
      formattedError = new AppError(
        process.env.NODE_ENV === 'production' 
          ? 'Internal server error' 
          : error.message,
        500,
        'INTERNAL_SERVER_ERROR',
        process.env.NODE_ENV === 'development' ? { originalError: error.message } : null
      );
    }
  }

  // Get language preference from request headers
  const language = req.headers['accept-language']?.includes('ar') ? 'ar' : 'en';

  // Create error response
  const errorResponse = createErrorResponse(formattedError, language);

  // Log security-related errors
  if (formattedError.statusCode === 401 || formattedError.statusCode === 403) {
    logger.security('Security error occurred', {
      error: formattedError.message,
      code: formattedError.code,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
      method: req.method,
      userId: req.user?.id
    });
  }

  // Send error response
  res.status(formattedError.statusCode || 500).json(errorResponse);
};

/**
 * Handle 404 errors
 */
const notFoundHandler = (req, res) => {
  const language = req.headers['accept-language']?.includes('ar') ? 'ar' : 'en';
  
  const error = new AppError(
    language === 'ar' 
      ? 'المسار المطلوب غير موجود' 
      : 'Route not found',
    404,
    'ROUTE_NOT_FOUND'
  );

  const errorResponse = createErrorResponse(error, language);
  
  logger.warn('Route not found', {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(404).json(errorResponse);
};

/**
 * Async error wrapper
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Wrapped function
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler,
  handleDatabaseError,
  handleJWTError,
  handleValidationError,
  handleMulterError,
  handleRedisError
};
