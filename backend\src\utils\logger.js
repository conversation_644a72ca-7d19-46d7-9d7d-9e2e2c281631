/**
 * <PERSON>gger Configuration
 * إعدادات نظام السجلات باستخدام Winston
 */

const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

// Create logs directory if it doesn't exist
const logDir = process.env.LOG_FILE_PATH || 'logs';
require('fs').mkdirSync(logDir, { recursive: true });

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each log level
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Add colors to winston
winston.addColors(logColors);

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    const { timestamp, level, message, stack, ...meta } = info;
    
    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // Add stack trace for errors
    if (stack) {
      logMessage += `\nStack: ${stack}`;
    }
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += `\nMeta: ${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf((info) => {
    const { timestamp, level, message, stack, ...meta } = info;
    
    let logMessage = `${timestamp} [${level}]: ${message}`;
    
    if (stack) {
      logMessage += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
);

// Create transports
const transports = [];

// Console transport (for development)
if (process.env.NODE_ENV !== 'production') {
  transports.push(
    new winston.transports.Console({
      level: process.env.LOG_LEVEL || 'debug',
      format: consoleFormat,
    })
  );
}

// File transport for all logs
transports.push(
  new DailyRotateFile({
    filename: path.join(logDir, 'application-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d',
    level: 'debug',
    format: logFormat,
    auditFile: path.join(logDir, 'audit.json'),
  })
);

// Error logs transport
transports.push(
  new DailyRotateFile({
    filename: path.join(logDir, 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d',
    level: 'error',
    format: logFormat,
    auditFile: path.join(logDir, 'error-audit.json'),
  })
);

// HTTP logs transport
transports.push(
  new DailyRotateFile({
    filename: path.join(logDir, 'http-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '14d',
    level: 'http',
    format: logFormat,
    auditFile: path.join(logDir, 'http-audit.json'),
  })
);

// Security logs transport
transports.push(
  new DailyRotateFile({
    filename: path.join(logDir, 'security-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: process.env.LOG_MAX_FILES || '30d',
    level: 'warn',
    format: logFormat,
    auditFile: path.join(logDir, 'security-audit.json'),
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels: logLevels,
  format: logFormat,
  transports,
  exitOnError: false,
});

// Handle uncaught exceptions and unhandled rejections
logger.exceptions.handle(
  new DailyRotateFile({
    filename: path.join(logDir, 'exceptions-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '30d',
    format: logFormat,
  })
);

logger.rejections.handle(
  new DailyRotateFile({
    filename: path.join(logDir, 'rejections-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '30d',
    format: logFormat,
  })
);

// Custom logging methods
const customLogger = {
  // Standard logging methods
  error: (message, meta = {}) => logger.error(message, meta),
  warn: (message, meta = {}) => logger.warn(message, meta),
  info: (message, meta = {}) => logger.info(message, meta),
  http: (message, meta = {}) => logger.http(message, meta),
  debug: (message, meta = {}) => logger.debug(message, meta),

  // Security-specific logging
  security: (message, meta = {}) => {
    logger.warn(`[SECURITY] ${message}`, {
      ...meta,
      category: 'security',
      timestamp: new Date().toISOString(),
    });
  },

  // Audit logging for financial transactions
  audit: (action, userId, details = {}) => {
    logger.info(`[AUDIT] ${action}`, {
      userId,
      action,
      details,
      category: 'audit',
      timestamp: new Date().toISOString(),
    });
  },

  // Performance logging
  performance: (operation, duration, meta = {}) => {
    logger.info(`[PERFORMANCE] ${operation} completed in ${duration}ms`, {
      operation,
      duration,
      ...meta,
      category: 'performance',
      timestamp: new Date().toISOString(),
    });
  },

  // Database operation logging
  database: (operation, table, meta = {}) => {
    logger.debug(`[DATABASE] ${operation} on ${table}`, {
      operation,
      table,
      ...meta,
      category: 'database',
      timestamp: new Date().toISOString(),
    });
  },

  // API request logging
  api: (method, endpoint, statusCode, responseTime, meta = {}) => {
    logger.http(`[API] ${method} ${endpoint} - ${statusCode} (${responseTime}ms)`, {
      method,
      endpoint,
      statusCode,
      responseTime,
      ...meta,
      category: 'api',
      timestamp: new Date().toISOString(),
    });
  },

  // Business logic logging
  business: (event, details = {}) => {
    logger.info(`[BUSINESS] ${event}`, {
      event,
      details,
      category: 'business',
      timestamp: new Date().toISOString(),
    });
  },

  // System health logging
  health: (component, status, details = {}) => {
    const level = status === 'healthy' ? 'info' : 'warn';
    logger[level](`[HEALTH] ${component} is ${status}`, {
      component,
      status,
      details,
      category: 'health',
      timestamp: new Date().toISOString(),
    });
  },
};

// Export logger
module.exports = customLogger;
