/**
 * Redis Configuration
 * إعدادات Redis للتخزين المؤقت والجلسات
 */

const Redis = require('ioredis');
const logger = require('../utils/logger');

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || null,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
  retryDelayOnClusterDown: 300,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  family: 4, // 4 (IPv4) or 6 (IPv6)
};

// Create Redis client
const redisClient = new Redis(redisConfig);

// Redis event handlers
redisClient.on('connect', () => {
  logger.info('✅ Redis client connected');
});

redisClient.on('ready', () => {
  logger.info('✅ Redis client ready');
});

redisClient.on('error', (error) => {
  logger.error('❌ Redis client error:', error.message);
});

redisClient.on('close', () => {
  logger.warn('⚠️ Redis client connection closed');
});

redisClient.on('reconnecting', () => {
  logger.info('🔄 Redis client reconnecting...');
});

redisClient.on('end', () => {
  logger.warn('⚠️ Redis client connection ended');
});

// Redis utility functions
const redisUtils = {
  /**
   * Set a key-value pair with optional expiration
   * @param {string} key - Redis key
   * @param {any} value - Value to store
   * @param {number} ttl - Time to live in seconds (optional)
   */
  async set(key, value, ttl = null) {
    try {
      const serializedValue = JSON.stringify(value);
      if (ttl) {
        await redisClient.setex(key, ttl, serializedValue);
      } else {
        await redisClient.set(key, serializedValue);
      }
      return true;
    } catch (error) {
      logger.error('Redis SET error:', error.message);
      return false;
    }
  },

  /**
   * Get a value by key
   * @param {string} key - Redis key
   */
  async get(key) {
    try {
      const value = await redisClient.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Redis GET error:', error.message);
      return null;
    }
  },

  /**
   * Delete a key
   * @param {string} key - Redis key
   */
  async del(key) {
    try {
      const result = await redisClient.del(key);
      return result > 0;
    } catch (error) {
      logger.error('Redis DEL error:', error.message);
      return false;
    }
  },

  /**
   * Check if key exists
   * @param {string} key - Redis key
   */
  async exists(key) {
    try {
      const result = await redisClient.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXISTS error:', error.message);
      return false;
    }
  },

  /**
   * Set expiration for a key
   * @param {string} key - Redis key
   * @param {number} ttl - Time to live in seconds
   */
  async expire(key, ttl) {
    try {
      const result = await redisClient.expire(key, ttl);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXPIRE error:', error.message);
      return false;
    }
  },

  /**
   * Increment a numeric value
   * @param {string} key - Redis key
   * @param {number} increment - Increment value (default: 1)
   */
  async incr(key, increment = 1) {
    try {
      if (increment === 1) {
        return await redisClient.incr(key);
      } else {
        return await redisClient.incrby(key, increment);
      }
    } catch (error) {
      logger.error('Redis INCR error:', error.message);
      return null;
    }
  },

  /**
   * Add to a set
   * @param {string} key - Redis key
   * @param {string|array} members - Member(s) to add
   */
  async sadd(key, members) {
    try {
      if (Array.isArray(members)) {
        return await redisClient.sadd(key, ...members);
      } else {
        return await redisClient.sadd(key, members);
      }
    } catch (error) {
      logger.error('Redis SADD error:', error.message);
      return null;
    }
  },

  /**
   * Get all members of a set
   * @param {string} key - Redis key
   */
  async smembers(key) {
    try {
      return await redisClient.smembers(key);
    } catch (error) {
      logger.error('Redis SMEMBERS error:', error.message);
      return [];
    }
  },

  /**
   * Push to a list
   * @param {string} key - Redis key
   * @param {any} value - Value to push
   */
  async lpush(key, value) {
    try {
      const serializedValue = JSON.stringify(value);
      return await redisClient.lpush(key, serializedValue);
    } catch (error) {
      logger.error('Redis LPUSH error:', error.message);
      return null;
    }
  },

  /**
   * Pop from a list
   * @param {string} key - Redis key
   */
  async lpop(key) {
    try {
      const value = await redisClient.lpop(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Redis LPOP error:', error.message);
      return null;
    }
  },

  /**
   * Get list length
   * @param {string} key - Redis key
   */
  async llen(key) {
    try {
      return await redisClient.llen(key);
    } catch (error) {
      logger.error('Redis LLEN error:', error.message);
      return 0;
    }
  },

  /**
   * Flush all data (use with caution!)
   */
  async flushall() {
    try {
      return await redisClient.flushall();
    } catch (error) {
      logger.error('Redis FLUSHALL error:', error.message);
      return false;
    }
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Closing Redis connection...');
  await redisClient.quit();
  logger.info('Redis connection closed');
});

module.exports = {
  redisClient,
  redisUtils
};
