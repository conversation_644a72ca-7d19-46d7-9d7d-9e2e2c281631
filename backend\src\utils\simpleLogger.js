/**
 * Simple Logger for Development
 * نظام سجلات مبسط للتطوير
 */

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

const getTimestamp = () => {
  return new Date().toISOString();
};

const formatMessage = (level, message, meta = {}) => {
  const timestamp = getTimestamp();
  const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
  return `${timestamp} [${level}]: ${message}${metaStr}`;
};

const logger = {
  error: (message, meta = {}) => {
    console.error(`${colors.red}${formatMessage('ERROR', message, meta)}${colors.reset}`);
  },
  
  warn: (message, meta = {}) => {
    console.warn(`${colors.yellow}${formatMessage('WARN', message, meta)}${colors.reset}`);
  },
  
  info: (message, meta = {}) => {
    console.info(`${colors.green}${formatMessage('INFO', message, meta)}${colors.reset}`);
  },
  
  debug: (message, meta = {}) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`${colors.blue}${formatMessage('DEBUG', message, meta)}${colors.reset}`);
    }
  },
  
  http: (message, meta = {}) => {
    console.log(`${colors.cyan}${formatMessage('HTTP', message, meta)}${colors.reset}`);
  },
  
  // Custom methods
  security: (message, meta = {}) => {
    console.log(`${colors.magenta}${formatMessage('SECURITY', message, meta)}${colors.reset}`);
  },
  
  audit: (action, userId, details = {}) => {
    const message = `${action} by user ${userId}`;
    console.log(`${colors.bright}${formatMessage('AUDIT', message, details)}${colors.reset}`);
  },
  
  performance: (operation, duration, meta = {}) => {
    const message = `${operation} completed in ${duration}ms`;
    console.log(`${colors.dim}${formatMessage('PERF', message, meta)}${colors.reset}`);
  },
  
  database: (operation, table, meta = {}) => {
    const message = `${operation} on ${table}`;
    console.log(`${colors.cyan}${formatMessage('DB', message, meta)}${colors.reset}`);
  },
  
  api: (method, endpoint, statusCode, responseTime, meta = {}) => {
    const message = `${method} ${endpoint} - ${statusCode} (${responseTime}ms)`;
    console.log(`${colors.green}${formatMessage('API', message, meta)}${colors.reset}`);
  },
  
  business: (event, details = {}) => {
    console.log(`${colors.blue}${formatMessage('BUSINESS', event, details)}${colors.reset}`);
  },
  
  health: (component, status, details = {}) => {
    const color = status === 'healthy' ? colors.green : colors.yellow;
    const message = `${component} is ${status}`;
    console.log(`${color}${formatMessage('HEALTH', message, details)}${colors.reset}`);
  }
};

module.exports = logger;
