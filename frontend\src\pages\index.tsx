/**
 * Home Page - Landing Page
 * الصفحة الرئيسية
 */

import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetStaticProps } from 'next';
import { motion } from 'framer-motion';
import {
  ArrowRightIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
  ClockIcon,
  UserGroupIcon,
  CheckCircleIcon,
  StarIcon
} from '@heroicons/react/24/outline';

// Components
import Layout from '@/components/Layout/Layout';
import Button from '@/components/UI/Button';
import Card from '@/components/UI/Card';
import Container from '@/components/UI/Container';

const HomePage: React.FC = () => {
  const { t } = useTranslation('common');

  const features = [
    {
      icon: ShieldCheckIcon,
      title: t('features.security.title'),
      description: t('features.security.description'),
      color: 'text-primary-600'
    },
    {
      icon: GlobeAltIcon,
      title: t('features.global.title'),
      description: t('features.global.description'),
      color: 'text-success-600'
    },
    {
      icon: CurrencyDollarIcon,
      title: t('features.rates.title'),
      description: t('features.rates.description'),
      color: 'text-warning-600'
    },
    {
      icon: ClockIcon,
      title: t('features.speed.title'),
      description: t('features.speed.description'),
      color: 'text-error-600'
    },
    {
      icon: UserGroupIcon,
      title: t('features.support.title'),
      description: t('features.support.description'),
      color: 'text-secondary-600'
    },
    {
      icon: CheckCircleIcon,
      title: t('features.reliability.title'),
      description: t('features.reliability.description'),
      color: 'text-primary-600'
    }
  ];

  const stats = [
    { value: '1M+', label: t('stats.customers') },
    { value: '200+', label: t('stats.countries') },
    { value: '$50B+', label: t('stats.transferred') },
    { value: '99.9%', label: t('stats.uptime') }
  ];

  const testimonials = [
    {
      name: 'Ahmed Al-Rashid',
      role: t('testimonials.customer'),
      content: t('testimonials.testimonial1'),
      rating: 5
    },
    {
      name: 'Sarah Johnson',
      role: t('testimonials.customer'),
      content: t('testimonials.testimonial2'),
      rating: 5
    },
    {
      name: 'Mohammed Hassan',
      role: t('testimonials.customer'),
      content: t('testimonials.testimonial3'),
      rating: 5
    }
  ];

  return (
    <Layout>
      <Head>
        <title>{t('meta.home.title')} | Wistron Money Transfer</title>
        <meta name="description" content={t('meta.home.description')} />
        <meta name="keywords" content={t('meta.home.keywords')} />
        <meta property="og:title" content={t('meta.home.title')} />
        <meta property="og:description" content={t('meta.home.description')} />
        <meta property="og:type" content="website" />
        <link rel="canonical" href="https://wistron.com" />
      </Head>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <Container className="relative py-20 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight">
                {t('hero.title')}
              </h1>
              <p className="text-xl lg:text-2xl mb-8 text-primary-100">
                {t('hero.subtitle')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/auth/register">
                  <Button size="lg" className="bg-white text-primary-600 hover:bg-primary-50">
                    {t('hero.cta.register')}
                    <ArrowRightIcon className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
                <Link href="/send-money">
                  <Button 
                    size="lg" 
                    variant="outline" 
                    className="border-white text-white hover:bg-white hover:text-primary-600"
                  >
                    {t('hero.cta.send')}
                  </Button>
                </Link>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h3 className="text-2xl font-semibold mb-6">{t('hero.calculator.title')}</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">{t('hero.calculator.send')}</label>
                    <div className="flex">
                      <input 
                        type="number" 
                        placeholder="1000"
                        className="flex-1 px-4 py-3 rounded-l-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                      />
                      <select className="px-4 py-3 rounded-r-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-white/50">
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">{t('hero.calculator.receive')}</label>
                    <div className="flex">
                      <input 
                        type="number" 
                        placeholder="3750"
                        className="flex-1 px-4 py-3 rounded-l-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50"
                        readOnly
                      />
                      <select className="px-4 py-3 rounded-r-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-white/50">
                        <option value="SAR">SAR</option>
                        <option value="AED">AED</option>
                        <option value="EGP">EGP</option>
                      </select>
                    </div>
                  </div>
                  <div className="text-sm text-primary-100">
                    {t('hero.calculator.rate')}: 1 USD = 3.75 SAR
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-neutral-50 dark:bg-neutral-900">
        <Container>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl lg:text-4xl font-bold text-primary-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-neutral-600 dark:text-neutral-400">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </Container>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <Container>
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-white mb-4"
            >
              {t('features.title')}
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto"
            >
              {t('features.subtitle')}
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full p-8 hover:shadow-medium transition-shadow duration-300">
                  <feature.icon className={`w-12 h-12 ${feature.color} mb-6`} />
                  <h3 className="text-xl font-semibold text-neutral-900 dark:text-white mb-4">
                    {feature.title}
                  </h3>
                  <p className="text-neutral-600 dark:text-neutral-400">
                    {feature.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </Container>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-neutral-50 dark:bg-neutral-900">
        <Container>
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-white mb-4"
            >
              {t('testimonials.title')}
            </motion.h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full p-8">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <StarIcon key={i} className="w-5 h-5 text-warning-500 fill-current" />
                    ))}
                  </div>
                  <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <div className="font-semibold text-neutral-900 dark:text-white">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                      {testimonial.role}
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-secondary-600 text-white">
        <Container>
          <div className="text-center">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl lg:text-4xl font-bold mb-6"
            >
              {t('cta.title')}
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-xl mb-8 text-primary-100"
            >
              {t('cta.subtitle')}
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Link href="/auth/register">
                <Button size="lg" className="bg-white text-primary-600 hover:bg-primary-50">
                  {t('cta.button')}
                  <ArrowRightIcon className="w-5 h-5 ml-2" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </Container>
      </section>
    </Layout>
  );
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'en', ['common'])),
    },
  };
};

export default HomePage;
