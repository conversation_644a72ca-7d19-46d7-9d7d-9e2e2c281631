# 💰 نظام تحويل الأموال المتكامل - Wistron Money Transfer System

## 🎯 نظرة عامة
نظام رقمي متكامل لتحويل الأموال محليًا ودوليًا، مصمم بأحدث التقنيات لضمان الأمان والموثوقية والأداء العالي.

## 🏗️ البنية التقنية

### Backend
- **Framework**: Node.js + Express.js
- **Database**: PostgreSQL (المعاملات) + Redis (Cache)
- **Authentication**: JWT + OAuth 2.0 + 2FA
- **API**: RESTful API + GraphQL
- **Security**: TLS 1.3, End-to-End Encryption

### Frontend
- **Web**: Next.js + React.js + Tailwind CSS
- **Mobile**: React Native (iOS + Android)
- **UI/UX**: Responsive Design + Dark/Light Mode

### Infrastructure
- **Cloud**: AWS/Azure with Auto-scaling
- **Containerization**: Docker + Kubernetes
- **CI/CD**: GitHub Actions
- **Monitoring**: Prometheus + Grafana

## 📁 هيكل المشروع

```
Wistron/
├── backend/                 # Node.js API Server
│   ├── src/
│   │   ├── controllers/     # Route Controllers
│   │   ├── models/         # Database Models
│   │   ├── middleware/     # Custom Middleware
│   │   ├── services/       # Business Logic
│   │   ├── utils/          # Helper Functions
│   │   └── config/         # Configuration Files
│   ├── tests/              # Unit & Integration Tests
│   └── docs/               # API Documentation
├── frontend/               # Next.js Web Application
│   ├── components/         # React Components
│   ├── pages/             # Next.js Pages
│   ├── styles/            # CSS/Tailwind Styles
│   ├── hooks/             # Custom React Hooks
│   ├── utils/             # Helper Functions
│   └── public/            # Static Assets
├── mobile/                # React Native App
│   ├── src/
│   │   ├── components/    # React Native Components
│   │   ├── screens/       # App Screens
│   │   ├── navigation/    # Navigation Setup
│   │   └── services/      # API Services
│   └── assets/            # Mobile Assets
├── database/              # Database Scripts
│   ├── migrations/        # Database Migrations
│   ├── seeds/            # Sample Data
│   └── schemas/          # Database Schemas
├── docker/               # Docker Configuration
├── docs/                 # Project Documentation
└── scripts/              # Deployment Scripts
```

## 🔐 الميزات الأمنية

- **تشفير شامل**: End-to-End encryption لجميع المعاملات
- **KYC/AML**: نظام التحقق من الهوية ومكافحة غسيل الأموال
- **Fraud Detection**: كشف الاحتيال والأنشطة المشبوهة
- **2FA**: مصادقة ثنائية العامل
- **Rate Limiting**: حماية من الهجمات
- **Audit Logging**: سجل كامل لجميع العمليات

## 🚀 الوظائف الرئيسية

### للمستخدمين
- ✅ إنشاء حساب وتوثيقه
- ✅ إرسال حوالات محلية ودولية
- ✅ تتبع الحوالات لحظيًا
- ✅ إدارة الملف الشخصي
- ✅ سجل المعاملات

### للوكلاء
- ✅ لوحة تحكم متقدمة
- ✅ إدارة معاملات العملاء
- ✅ تسجيل عمليات الاستلام والتسليم
- ✅ تقارير الفرع

### للإدارة
- ✅ إدارة النظام الكامل
- ✅ مراقبة الأداء
- ✅ التقارير المالية والإدارية
- ✅ إدارة المستخدمين والصلاحيات

## 📊 التقارير والتحليلات
- لوحة بيانات تحليلية شاملة
- تقارير مالية دورية
- مراقبة الأداء في الوقت الفعلي
- تحليل سلوك المستخدمين

## 🌍 الامتثال التنظيمي
- معايير KYC/AML العالمية
- قيود جغرافية قابلة للتخصيص
- دعم التدقيق المالي
- سجل مراجعة شامل

## 🛠️ التطوير والنشر

### متطلبات التطوير
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Docker & Docker Compose

### تشغيل المشروع محليًا

#### الطريقة الأولى: باستخدام Docker (الأسهل)
```bash
# استنساخ المشروع
git clone <repository-url>
cd Wistron

# تشغيل جميع الخدمات
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f
```

#### الطريقة الثانية: التشغيل المحلي
```bash
# 1. تشغيل قاعدة البيانات والـ Redis
docker-compose up -d postgres redis

# 2. إعداد Backend
cd backend
cp .env.example .env
npm install
npm run migrate
npm run seed
npm run dev

# 3. إعداد Frontend (في terminal جديد)
cd frontend
npm install
npm run dev
```

### الوصول للتطبيق
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api-docs
- **Database**: localhost:5432
- **Redis**: localhost:6379
- **Grafana**: http://localhost:3001 (admin/wistron_grafana_admin_2024)
- **Prometheus**: http://localhost:9090

### حسابات التجربة
- **Admin**: <EMAIL> / Admin@123456
- **Agent**: <EMAIL> / Agent@123456

## 📝 الترخيص
هذا المشروع محمي بحقوق الطبع والنشر لشركة Wistron.

## 🤝 المساهمة
يرجى مراجعة دليل المساهمة قبل إرسال أي تعديلات.

---
**تم التطوير بواسطة فريق Wistron التقني** 🚀
