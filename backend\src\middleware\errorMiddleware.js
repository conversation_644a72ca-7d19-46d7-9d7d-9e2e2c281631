/**
 * Global Error Handler Middleware
 * وسطاء معالجة الأخطاء العام
 */

const logger = require('../utils/logger');
const { 
  AppError, 
  createErrorResponse, 
  isOperationalError,
  ERROR_CODES 
} = require('../utils/errors');

/**
 * Handle Knex/PostgreSQL database errors
 */
const handleDatabaseError = (error) => {
  logger.database('Database error occurred', { error: error.message, code: error.code });

  switch (error.code) {
    case '23505': // Unique violation
      return new AppError('Resource already exists', 409, ERROR_CODES.DUPLICATE_TRANSACTION);
    case '23503': // Foreign key violation
      return new AppError('Referenced resource not found', 400, ERROR_CODES.RECIPIENT_NOT_FOUND);
    case '23502': // Not null violation
      return new AppError('Required field is missing', 400, ERROR_CODES.REQUIRED_FIELD_MISSING);
    default:
      return new AppError('Database operation failed', 500, ERROR_CODES.DATABASE_CONNECTION_ERROR);
  }
};

/**
 * Handle JWT errors
 */
const handleJWTError = (error) => {
  logger.security('JWT error occurred', { error: error.message });

  if (error.name === 'TokenExpiredError') {
    return new AppError('Token has expired', 401, ERROR_CODES.TOKEN_EXPIRED);
  }
  
  if (error.name === 'JsonWebTokenError') {
    return new AppError('Invalid token', 401, ERROR_CODES.TOKEN_INVALID);
  }
  
  return new AppError('Authentication failed', 401, ERROR_CODES.TOKEN_INVALID);
};

/**
 * Handle validation errors
 */
const handleValidationError = (error) => {
  logger.warn('Validation error occurred', { error: error.message });

  if (error.isJoi) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value
    }));
    
    return new AppError('Validation failed', 400, ERROR_CODES.INVALID_FORMAT, { validationErrors: details });
  }

  return new AppError('Validation failed', 400, ERROR_CODES.INVALID_FORMAT);
};

/**
 * Main error handler middleware
 */
const errorHandler = (error, req, res, next) => {
  let formattedError = error;

  // Log the original error
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString()
  });

  // Handle specific error types
  if (!isOperationalError(error)) {
    if (error.code && (error.code.startsWith('23') || error.code.startsWith('08') || error.code === '40001')) {
      formattedError = handleDatabaseError(error);
    }
    else if (error.name && error.name.includes('Token')) {
      formattedError = handleJWTError(error);
    }
    else if (error.isJoi || (error.array && typeof error.array === 'function')) {
      formattedError = handleValidationError(error);
    }
    else {
      formattedError = new AppError(
        process.env.NODE_ENV === 'production' ? 'Internal server error' : error.message,
        500,
        'INTERNAL_SERVER_ERROR'
      );
    }
  }

  // Get language preference
  const language = req.headers['accept-language']?.includes('ar') ? 'ar' : 'en';
  const errorResponse = createErrorResponse(formattedError, language);

  // Log security-related errors
  if (formattedError.statusCode === 401 || formattedError.statusCode === 403) {
    logger.security('Security error occurred', {
      error: formattedError.message,
      code: formattedError.code,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
      method: req.method,
      userId: req.user?.id
    });
  }

  res.status(formattedError.statusCode || 500).json(errorResponse);
};

/**
 * Async error wrapper
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  errorHandler,
  asyncHandler,
  handleDatabaseError,
  handleJWTError,
  handleValidationError
};
