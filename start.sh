#!/bin/bash

# Wistron Money Transfer System - Quick Start Script
# سكريبت التشغيل السريع لنظام ويسترون لتحويل الأموال

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_status "Docker and Docker Compose are installed ✓"
}

# Check if Node.js is installed (for local development)
check_nodejs() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js version: $NODE_VERSION ✓"
    else
        print_warning "Node.js is not installed. Docker mode will be used."
    fi
}

# Create environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend .env
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_status "Created backend/.env from example"
    else
        print_warning "backend/.env already exists, skipping..."
    fi
    
    # Frontend .env.local
    if [ ! -f "frontend/.env.local" ]; then
        cat > frontend/.env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:5000/api/v1
NEXT_PUBLIC_APP_NAME=Wistron Money Transfer
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_SOCKET_URL=http://localhost:5000
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=wistron_nextauth_secret_2024
EOF
        print_status "Created frontend/.env.local"
    else
        print_warning "frontend/.env.local already exists, skipping..."
    fi
}

# Start services with Docker
start_docker() {
    print_header "Starting Wistron Money Transfer with Docker"
    
    print_status "Building and starting all services..."
    docker-compose up -d --build
    
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        print_status "Services are starting up..."
        
        # Wait for database to be ready
        print_status "Waiting for database to be ready..."
        until docker-compose exec -T postgres pg_isready -U wistron_user -d wistron_money_transfer; do
            sleep 2
        done
        
        print_status "Database is ready ✓"
        
        # Run migrations
        print_status "Running database migrations..."
        docker-compose exec -T backend npm run migrate || print_warning "Migration failed or already applied"
        
        # Seed database
        print_status "Seeding database with initial data..."
        docker-compose exec -T backend npm run seed || print_warning "Seeding failed or already applied"
        
        print_header "🚀 Wistron Money Transfer is now running!"
        echo ""
        echo -e "${GREEN}Frontend:${NC} http://localhost:3000"
        echo -e "${GREEN}Backend API:${NC} http://localhost:5000"
        echo -e "${GREEN}API Documentation:${NC} http://localhost:5000/api-docs"
        echo -e "${GREEN}Grafana Dashboard:${NC} http://localhost:3001 (admin/wistron_grafana_admin_2024)"
        echo -e "${GREEN}Prometheus:${NC} http://localhost:9090"
        echo ""
        echo -e "${BLUE}Test Accounts:${NC}"
        echo -e "Admin: <EMAIL> / Admin@123456"
        echo -e "Agent: <EMAIL> / Agent@123456"
        echo ""
        echo -e "${YELLOW}To stop the services:${NC} docker-compose down"
        echo -e "${YELLOW}To view logs:${NC} docker-compose logs -f"
        
    else
        print_error "Failed to start services. Check docker-compose logs for details."
        docker-compose logs
        exit 1
    fi
}

# Start services locally (development mode)
start_local() {
    print_header "Starting Wistron Money Transfer in Development Mode"
    
    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        print_error "Node.js is required for local development. Please install Node.js 18+ or use Docker mode."
        exit 1
    fi
    
    # Start database and Redis with Docker
    print_status "Starting database and Redis..."
    docker-compose up -d postgres redis
    
    # Wait for database
    print_status "Waiting for database to be ready..."
    until docker-compose exec -T postgres pg_isready -U wistron_user -d wistron_money_transfer; do
        sleep 2
    done
    
    # Install backend dependencies
    if [ ! -d "backend/node_modules" ]; then
        print_status "Installing backend dependencies..."
        cd backend && npm install && cd ..
    fi
    
    # Install frontend dependencies
    if [ ! -d "frontend/node_modules" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend && npm install && cd ..
    fi
    
    # Run migrations
    print_status "Running database migrations..."
    cd backend && npm run migrate && cd ..
    
    # Seed database
    print_status "Seeding database..."
    cd backend && npm run seed && cd ..
    
    print_header "🚀 Starting development servers..."
    
    # Start backend and frontend in background
    cd backend && npm run dev &
    BACKEND_PID=$!
    
    cd frontend && npm run dev &
    FRONTEND_PID=$!
    
    # Wait a bit for servers to start
    sleep 5
    
    print_header "✅ Development servers are running!"
    echo ""
    echo -e "${GREEN}Frontend:${NC} http://localhost:3000"
    echo -e "${GREEN}Backend API:${NC} http://localhost:5000"
    echo -e "${GREEN}API Documentation:${NC} http://localhost:5000/api-docs"
    echo ""
    echo -e "${BLUE}Test Accounts:${NC}"
    echo -e "Admin: <EMAIL> / Admin@123456"
    echo -e "Agent: <EMAIL> / Agent@123456"
    echo ""
    echo -e "${YELLOW}Press Ctrl+C to stop all services${NC}"
    
    # Wait for user to stop
    trap "print_status 'Stopping services...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; docker-compose stop postgres redis; exit 0" INT
    wait
}

# Health check
health_check() {
    print_header "Running Health Check"
    
    # Check if services are running
    if curl -s http://localhost:5000/health > /dev/null; then
        print_status "Backend API is healthy ✓"
    else
        print_error "Backend API is not responding ✗"
    fi
    
    if curl -s http://localhost:3000 > /dev/null; then
        print_status "Frontend is healthy ✓"
    else
        print_error "Frontend is not responding ✗"
    fi
}

# Show help
show_help() {
    echo "Wistron Money Transfer - Quick Start Script"
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  docker    Start with Docker (recommended)"
    echo "  local     Start in local development mode"
    echo "  health    Run health check"
    echo "  stop      Stop all services"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 docker    # Start with Docker"
    echo "  $0 local     # Start in development mode"
    echo "  $0 health    # Check service health"
}

# Stop services
stop_services() {
    print_header "Stopping Wistron Money Transfer Services"
    
    print_status "Stopping Docker services..."
    docker-compose down
    
    print_status "Killing any remaining Node.js processes..."
    pkill -f "node.*server.js" 2>/dev/null || true
    pkill -f "npm.*dev" 2>/dev/null || true
    
    print_status "All services stopped ✓"
}

# Main script logic
main() {
    print_header "🏦 Wistron Money Transfer System"
    print_status "نظام ويسترون المتكامل لتحويل الأموال"
    echo ""
    
    # Check prerequisites
    check_docker
    check_nodejs
    
    # Setup environment files
    setup_env_files
    
    case "${1:-docker}" in
        "docker")
            start_docker
            ;;
        "local")
            start_local
            ;;
        "health")
            health_check
            ;;
        "stop")
            stop_services
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
