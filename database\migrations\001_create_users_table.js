/**
 * Users Table Migration
 * إنشاء جدول المستخدمين
 */

exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Basic information
    table.string('email').unique().notNullable();
    table.string('password_hash').notNullable();
    table.string('first_name').notNullable();
    table.string('last_name').notNullable();
    table.string('phone').unique();
    table.date('date_of_birth');
    table.enum('gender', ['male', 'female', 'other']);
    
    // Address information
    table.string('address_line1');
    table.string('address_line2');
    table.string('city');
    table.string('state');
    table.string('postal_code');
    table.string('country', 2); // ISO country code
    
    // Account information
    table.enum('role', ['customer', 'agent', 'admin', 'super_admin']).defaultTo('customer');
    table.enum('status', ['pending', 'active', 'suspended', 'closed']).defaultTo('pending');
    table.enum('kyc_status', ['not_started', 'pending', 'approved', 'rejected']).defaultTo('not_started');
    
    // Security
    table.boolean('is_2fa_enabled').defaultTo(false);
    table.boolean('is_email_verified').defaultTo(false);
    table.boolean('is_phone_verified').defaultTo(false);
    table.string('email_verification_token');
    table.string('phone_verification_token');
    table.timestamp('email_verified_at');
    table.timestamp('phone_verified_at');
    
    // Login tracking
    table.timestamp('last_login');
    table.string('last_login_ip');
    table.integer('failed_login_attempts').defaultTo(0);
    table.timestamp('locked_until');
    
    // Preferences
    table.string('preferred_language', 2).defaultTo('en');
    table.string('preferred_currency', 3).defaultTo('USD');
    table.string('timezone').defaultTo('UTC');
    
    // Metadata
    table.jsonb('metadata').defaultTo('{}');
    table.timestamps(true, true);
    
    // Indexes
    table.index(['email']);
    table.index(['phone']);
    table.index(['status']);
    table.index(['kyc_status']);
    table.index(['role']);
    table.index(['created_at']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('users');
};
