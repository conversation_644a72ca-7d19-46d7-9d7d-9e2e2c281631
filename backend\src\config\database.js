/**
 * Database Configuration - PostgreSQL with Knex.js
 * إعدادات قاعدة البيانات - PostgreSQL مع Knex.js
 */

const knex = require('knex');
const logger = require('../utils/logger');

const config = {
  development: {
    client: 'postgresql',
    connection: {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'wistron_money_transfer',
      user: process.env.DB_USER || 'wistron_user',
      password: process.env.DB_PASSWORD || 'wistron_secure_password_2024',
      ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    },
    pool: {
      min: parseInt(process.env.DB_POOL_MIN) || 2,
      max: parseInt(process.env.DB_POOL_MAX) || 10,
      acquireTimeoutMillis: 60000,
      createTimeoutMillis: 30000,
      destroyTimeoutMillis: 5000,
      idleTimeoutMillis: 30000,
      reapIntervalMillis: 1000,
      createRetryIntervalMillis: 100,
    },
    migrations: {
      directory: '../database/migrations',
      tableName: 'knex_migrations'
    },
    seeds: {
      directory: '../database/seeds'
    },
    debug: process.env.DEBUG_MODE === 'true',
    acquireConnectionTimeout: 60000,
    asyncStackTraces: true,
  },

  test: {
    client: 'postgresql',
    connection: {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME + '_test' || 'wistron_money_transfer_test',
      user: process.env.DB_USER || 'wistron_user',
      password: process.env.DB_PASSWORD || 'wistron_secure_password_2024',
      ssl: false,
    },
    pool: {
      min: 1,
      max: 5,
    },
    migrations: {
      directory: '../database/migrations',
      tableName: 'knex_migrations'
    },
    seeds: {
      directory: '../database/seeds'
    },
    debug: false,
  },

  production: {
    client: 'postgresql',
    connection: {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      ssl: { rejectUnauthorized: false },
    },
    pool: {
      min: parseInt(process.env.DB_POOL_MIN) || 5,
      max: parseInt(process.env.DB_POOL_MAX) || 20,
      acquireTimeoutMillis: 60000,
      createTimeoutMillis: 30000,
      destroyTimeoutMillis: 5000,
      idleTimeoutMillis: 30000,
      reapIntervalMillis: 1000,
      createRetryIntervalMillis: 100,
    },
    migrations: {
      directory: '../database/migrations',
      tableName: 'knex_migrations'
    },
    seeds: {
      directory: '../database/seeds'
    },
    debug: false,
    acquireConnectionTimeout: 60000,
  }
};

const environment = process.env.NODE_ENV || 'development';
const dbConfig = config[environment];

// Create database connection
const db = knex(dbConfig);

// Test database connection
db.raw('SELECT 1')
  .then(() => {
    logger.info(`✅ Database connected successfully (${environment})`);
  })
  .catch((error) => {
    logger.error('❌ Database connection failed:', error.message);
    process.exit(1);
  });

// Handle connection errors
db.on('query-error', (error, obj) => {
  logger.error('Database query error:', {
    error: error.message,
    sql: obj.sql,
    bindings: obj.bindings
  });
});

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Closing database connection...');
  await db.destroy();
  logger.info('Database connection closed');
});

module.exports = db;
