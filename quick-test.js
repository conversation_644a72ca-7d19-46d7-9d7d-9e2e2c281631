// Quick test for Wistron Complete System
const http = require('http');

console.log('🧪 Quick Test - Wistron Complete System\n');

function quickTest() {
  const req = http.request({
    hostname: 'localhost',
    port: 5000,
    path: '/health',
    method: 'GET',
    timeout: 5000
  }, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      console.log(`✅ Server Response: ${res.statusCode}`);
      try {
        const response = JSON.parse(data);
        console.log(`✅ Status: ${response.status}`);
        console.log(`✅ Message: ${response.message}`);
        console.log(`✅ Uptime: ${Math.floor(response.uptime)} seconds`);
        console.log(`✅ Users: ${response.stats.totalUsers}`);
        console.log(`✅ Transfers: ${response.stats.totalTransfers}`);
        
        console.log('\n🎉 ========================================');
        console.log('   WISTRON COMPLETE SYSTEM IS RUNNING!');
        console.log('   نظام ويسترون الكامل يعمل بنجاح!');
        console.log('========================================');
        console.log('🌐 Access Points:');
        console.log('   Dashboard: http://localhost:5000');
        console.log('   API Docs: http://localhost:5000/api-docs');
        console.log('   Health: http://localhost:5000/health');
        console.log('');
        console.log('👤 Test Accounts:');
        console.log('   Admin: <EMAIL> / Admin@123456');
        console.log('   Agent: <EMAIL> / Agent@123456');
        console.log('========================================');
        
      } catch (e) {
        console.log(`Response: ${data}`);
      }
    });
  });

  req.on('error', (err) => {
    console.log(`❌ Connection failed: ${err.message}`);
    console.log('Please make sure the server is running: node wistron-complete.js');
  });

  req.on('timeout', () => {
    console.log('❌ Request timeout');
    req.destroy();
  });

  req.end();
}

// Wait a moment then test
setTimeout(quickTest, 2000);
