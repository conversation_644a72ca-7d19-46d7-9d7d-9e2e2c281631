# Environment Configuration
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=wistron_money_transfer
DB_USER=wistron_user
DB_PASSWORD=wistron_secure_password_2024
DB_SSL=false
DB_POOL_MIN=2
DB_POOL_MAX=10

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=wistron_redis_password_2024
REDIS_DB=0
REDIS_TTL=3600

# JWT Configuration
JWT_SECRET=wistron_jwt_super_secret_key_2024_very_long_and_secure
JWT_REFRESH_SECRET=wistron_refresh_jwt_super_secret_key_2024_very_long_and_secure
JWT_EXPIRE=24h
JWT_REFRESH_EXPIRE=7d

# Encryption Configuration
ENCRYPTION_KEY=wistron_encryption_key_32_chars_long_2024
ENCRYPTION_ALGORITHM=aes-256-gcm

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
EMAIL_FROM=Wistron Money Transfer <<EMAIL>>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Payment Gateway Configuration
# Stripe
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# Exchange Rate API
EXCHANGE_RATE_API_KEY=your_exchange_rate_api_key
EXCHANGE_RATE_BASE_URL=https://api.exchangerate-api.com/v4/latest

# File Upload Configuration
UPLOAD_MAX_SIZE=********
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_PATH=uploads/

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
SESSION_SECRET=wistron_session_secret_key_2024

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=logs/
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# KYC/AML Configuration
KYC_VERIFICATION_REQUIRED=true
AML_DAILY_LIMIT=10000
AML_MONTHLY_LIMIT=50000
SUSPICIOUS_ACTIVITY_THRESHOLD=5000

# Notification Configuration
PUSH_NOTIFICATION_KEY=your_push_notification_key
WEBHOOK_SECRET=wistron_webhook_secret_2024

# OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# Development Configuration
SWAGGER_ENABLED=true
DEBUG_MODE=true
CORS_ORIGIN=http://localhost:3000

# Production Configuration (Override in production)
# NODE_ENV=production
# DB_SSL=true
# CORS_ORIGIN=https://yourdomain.com
# SWAGGER_ENABLED=false
# DEBUG_MODE=false
