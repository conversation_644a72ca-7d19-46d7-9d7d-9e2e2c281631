// Wistron Money Transfer - Stable Demo Server
const http = require('http');
const url = require('url');

console.log('🚀 Starting Wistron Money Transfer Server...');

const users = [
  { id: '1', email: '<EMAIL>', password: 'Admin@123456', role: 'admin', firstName: 'System', lastName: 'Admin' },
  { id: '2', email: '<EMAIL>', password: 'Agent@123456', role: 'agent', firstName: 'Sample', lastName: 'Agent' }
];

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`📝 ${method} ${path}`);

  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    // Routes
    if (path === '/' && method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <meta charset="UTF-8">
          <title>🏦 Wistron Money Transfer</title>
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              margin: 0; 
              padding: 0; 
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              min-height: 100vh;
            }
            .container { max-width: 1000px; margin: 0 auto; padding: 40px 20px; }
            .header { text-align: center; margin-bottom: 40px; }
            h1 { font-size: 3.5em; margin: 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
            .subtitle { font-size: 1.3em; margin: 20px 0; opacity: 0.9; }
            .status { 
              background: rgba(76, 175, 80, 0.2); 
              border: 2px solid #4CAF50; 
              border-radius: 10px; 
              padding: 20px; 
              margin: 30px 0; 
              text-align: center;
            }
            .cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 40px 0; }
            .card { 
              background: rgba(255,255,255,0.1); 
              padding: 30px; 
              border-radius: 15px; 
              backdrop-filter: blur(10px);
              border: 1px solid rgba(255,255,255,0.2);
            }
            .btn { 
              display: inline-block;
              padding: 12px 25px; 
              background: white; 
              color: #667eea; 
              text-decoration: none; 
              border-radius: 8px; 
              margin: 8px;
              font-weight: bold;
              transition: all 0.3s ease;
            }
            .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
            .endpoint { 
              background: rgba(0,0,0,0.2); 
              padding: 15px; 
              margin: 10px 0; 
              border-radius: 8px; 
              font-family: monospace;
            }
            .method { color: #4CAF50; font-weight: bold; }
            .accounts { background: rgba(255,193,7,0.2); border-left: 4px solid #FFC107; padding: 20px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🏦 Wistron Money Transfer</h1>
              <p class="subtitle">نظام ويسترون المتكامل لتحويل الأموال</p>
            </div>
            
            <div class="status">
              <h2>🚀 النظام يعمل بنجاح!</h2>
              <p><strong>✅ Server Status: RUNNING</strong></p>
              <p>الخادم التجريبي يعمل على المنفذ 5000</p>
              <p>وقت التشغيل: ${Math.floor(process.uptime())} ثانية</p>
            </div>
            
            <div class="cards">
              <div class="card">
                <h3>🔗 الروابط المهمة</h3>
                <a href="/api-docs" class="btn">📚 توثيق API</a>
                <a href="/health" class="btn">🏥 فحص الصحة</a>
                <a href="/api/v1/rates" class="btn">💱 أسعار الصرف</a>
                <a href="/api/v1/routes" class="btn">🗺️ قائمة المسارات</a>
              </div>
              
              <div class="card accounts">
                <h3>👤 حسابات التجربة</h3>
                <p><strong>المدير:</strong><br><EMAIL><br>Admin@123456</p>
                <p><strong>الوكيل:</strong><br><EMAIL><br>Agent@123456</p>
              </div>
              
              <div class="card">
                <h3>🔧 API Endpoints</h3>
                <div class="endpoint"><span class="method">GET</span> /health</div>
                <div class="endpoint"><span class="method">POST</span> /api/v1/auth/login</div>
                <div class="endpoint"><span class="method">POST</span> /api/v1/auth/register</div>
                <div class="endpoint"><span class="method">GET</span> /api/v1/rates</div>
                <div class="endpoint"><span class="method">GET</span> /api/v1/dashboard/stats</div>
              </div>
              
              <div class="card">
                <h3>⚠️ ملاحظة مهمة</h3>
                <p>هذا خادم تجريبي يستخدم الذاكرة المؤقتة</p>
                <p>البيانات ستفقد عند إعادة التشغيل</p>
                <p>للحصول على نظام كامل، يرجى تثبيت Docker</p>
              </div>
            </div>
          </div>
        </body>
        </html>
      `);
      return;
    }

    if (path === '/health' && method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        status: 'OK',
        message: 'Wistron Money Transfer Demo Server',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0-demo',
        users: users.length
      }));
      return;
    }

    if (path === '/api/v1/auth/login' && method === 'POST') {
      let body = '';
      req.on('data', chunk => body += chunk);
      req.on('end', () => {
        try {
          const { email, password } = JSON.parse(body);
          const user = users.find(u => u.email === email && u.password === password);
          
          if (!user) {
            res.writeHead(401, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: false, error: 'Invalid credentials' }));
            return;
          }
          
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: 'Login successful',
            data: {
              user: { 
                id: user.id, 
                email: user.email, 
                role: user.role,
                firstName: user.firstName,
                lastName: user.lastName
              },
              tokens: { accessToken: 'demo-token-' + user.id }
            }
          }));
        } catch (e) {
          res.writeHead(400, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
        }
      });
      return;
    }

    if (path === '/api/v1/rates' && method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        data: {
          base: 'USD',
          rates: { 
            SAR: 3.75, 
            AED: 3.67, 
            EGP: 30.85, 
            JOD: 0.71,
            KWD: 0.31,
            QAR: 3.64,
            BHD: 0.38,
            OMR: 0.38,
            EUR: 0.85, 
            GBP: 0.73 
          },
          lastUpdated: new Date().toISOString()
        }
      }));
      return;
    }

    if (path === '/api/v1/dashboard/stats' && method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        data: {
          totalUsers: users.length,
          activeUsers: users.length,
          totalTransfers: 0,
          completedTransfers: 0,
          totalVolume: 0,
          todayTransfers: 0,
          todayVolume: 0,
          serverUptime: Math.floor(process.uptime())
        }
      }));
      return;
    }

    if (path === '/api/v1/routes' && method === 'GET') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        data: {
          routes: [
            { method: 'GET', path: '/', description: 'الصفحة الرئيسية' },
            { method: 'GET', path: '/health', description: 'فحص صحة النظام' },
            { method: 'GET', path: '/api-docs', description: 'توثيق API' },
            { method: 'POST', path: '/api/v1/auth/login', description: 'تسجيل الدخول' },
            { method: 'GET', path: '/api/v1/rates', description: 'أسعار الصرف' },
            { method: 'GET', path: '/api/v1/dashboard/stats', description: 'إحصائيات النظام' },
            { method: 'GET', path: '/api/v1/routes', description: 'قائمة جميع المسارات' }
          ]
        }
      }));
      return;
    }

    // 404 - Route not found
    console.log(`❌ Route not found: ${method} ${path}`);
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      success: false, 
      error: 'Route not found',
      message: `المسار ${path} غير موجود`,
      requestedPath: path,
      requestedMethod: method,
      availableRoutes: [
        'GET /',
        'GET /health', 
        'POST /api/v1/auth/login',
        'GET /api/v1/rates',
        'GET /api/v1/dashboard/stats',
        'GET /api/v1/routes'
      ]
    }));

  } catch (error) {
    console.error('❌ Server error:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      success: false, 
      error: 'Internal server error',
      message: error.message 
    }));
  }
});

const PORT = 5000;

server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.log(`❌ Port ${PORT} is already in use. Trying to kill existing process...`);
    process.exit(1);
  } else {
    console.error('❌ Server error:', error);
  }
});

server.listen(PORT, () => {
  console.log(`
🏦 ========================================
   Wistron Money Transfer Demo Server
   نظام ويسترون لتحويل الأموال
========================================

🚀 Server running on: http://localhost:${PORT}
📚 API Documentation: http://localhost:${PORT}/api-docs
🏥 Health Check: http://localhost:${PORT}/health
🗺️  Available Routes: http://localhost:${PORT}/api/v1/routes

👤 Test Accounts:
   Admin: <EMAIL> / Admin@123456
   Agent: <EMAIL> / Agent@123456

⚠️  Demo server - No database required!
✅ Server is stable and ready!

========================================`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server gracefully...');
  server.close(() => {
    console.log('✅ Server closed successfully');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down...');
  server.close(() => {
    console.log('✅ Server closed successfully');
    process.exit(0);
  });
});
