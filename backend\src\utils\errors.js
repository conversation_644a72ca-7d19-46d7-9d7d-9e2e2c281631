/**
 * Custom Error Classes and Error Handling Utilities
 * فئات الأخطاء المخصصة وأدوات معالجة الأخطاء
 */

/**
 * Base Application Error Class
 */
class AppError extends Error {
  constructor(message, statusCode = 500, code = null, details = null) {
    super(message);
    
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;
    this.timestamp = new Date().toISOString();
    
    Error.captureStackTrace(this, this.constructor);
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      code: this.code,
      details: this.details,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * Validation Error Class
 */
class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

/**
 * Authentication Error Class
 */
class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

/**
 * Authorization Error Class
 */
class AuthorizationError extends AppError {
  constructor(message = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

/**
 * Not Found Error Class
 */
class NotFoundError extends AppError {
  constructor(resource = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND_ERROR');
  }
}

/**
 * Conflict Error Class
 */
class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

/**
 * Rate Limit Error Class
 */
class RateLimitError extends AppError {
  constructor(message = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

/**
 * Database Error Class
 */
class DatabaseError extends AppError {
  constructor(message = 'Database operation failed', details = null) {
    super(message, 500, 'DATABASE_ERROR', details);
  }
}

/**
 * External Service Error Class
 */
class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error', details = null) {
    super(`${service}: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR', details);
  }
}

/**
 * Business Logic Error Class
 */
class BusinessLogicError extends AppError {
  constructor(message, details = null) {
    super(message, 422, 'BUSINESS_LOGIC_ERROR', details);
  }
}

/**
 * Security Error Class
 */
class SecurityError extends AppError {
  constructor(message = 'Security violation detected') {
    super(message, 403, 'SECURITY_ERROR');
  }
}

/**
 * Payment Error Class
 */
class PaymentError extends AppError {
  constructor(message, details = null) {
    super(message, 402, 'PAYMENT_ERROR', details);
  }
}

/**
 * KYC Error Class
 */
class KYCError extends AppError {
  constructor(message, details = null) {
    super(message, 422, 'KYC_ERROR', details);
  }
}

/**
 * Transfer Error Class
 */
class TransferError extends AppError {
  constructor(message, details = null) {
    super(message, 422, 'TRANSFER_ERROR', details);
  }
}

/**
 * Error Code Constants
 */
const ERROR_CODES = {
  // Authentication & Authorization
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_SUSPENDED: 'ACCOUNT_SUSPENDED',
  TWO_FA_REQUIRED: 'TWO_FA_REQUIRED',
  TWO_FA_INVALID: 'TWO_FA_INVALID',
  
  // Validation
  REQUIRED_FIELD_MISSING: 'REQUIRED_FIELD_MISSING',
  INVALID_FORMAT: 'INVALID_FORMAT',
  INVALID_LENGTH: 'INVALID_LENGTH',
  INVALID_RANGE: 'INVALID_RANGE',
  
  // Business Logic
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  TRANSFER_LIMIT_EXCEEDED: 'TRANSFER_LIMIT_EXCEEDED',
  RECIPIENT_NOT_FOUND: 'RECIPIENT_NOT_FOUND',
  DUPLICATE_TRANSACTION: 'DUPLICATE_TRANSACTION',
  TRANSACTION_EXPIRED: 'TRANSACTION_EXPIRED',
  
  // KYC/AML
  KYC_INCOMPLETE: 'KYC_INCOMPLETE',
  KYC_REJECTED: 'KYC_REJECTED',
  AML_SUSPICIOUS_ACTIVITY: 'AML_SUSPICIOUS_ACTIVITY',
  DOCUMENT_VERIFICATION_FAILED: 'DOCUMENT_VERIFICATION_FAILED',
  
  // Payment
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_DECLINED: 'PAYMENT_DECLINED',
  PAYMENT_GATEWAY_ERROR: 'PAYMENT_GATEWAY_ERROR',
  INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
  
  // System
  DATABASE_CONNECTION_ERROR: 'DATABASE_CONNECTION_ERROR',
  REDIS_CONNECTION_ERROR: 'REDIS_CONNECTION_ERROR',
  EXTERNAL_SERVICE_UNAVAILABLE: 'EXTERNAL_SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // File Upload
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  FILE_UPLOAD_FAILED: 'FILE_UPLOAD_FAILED',
};

/**
 * Error Messages in Arabic and English
 */
const ERROR_MESSAGES = {
  [ERROR_CODES.INVALID_CREDENTIALS]: {
    en: 'Invalid email or password',
    ar: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
  },
  [ERROR_CODES.TOKEN_EXPIRED]: {
    en: 'Token has expired',
    ar: 'انتهت صلاحية الرمز المميز'
  },
  [ERROR_CODES.ACCOUNT_LOCKED]: {
    en: 'Account is locked due to multiple failed login attempts',
    ar: 'تم قفل الحساب بسبب محاولات تسجيل دخول فاشلة متعددة'
  },
  [ERROR_CODES.INSUFFICIENT_BALANCE]: {
    en: 'Insufficient balance for this transaction',
    ar: 'الرصيد غير كافي لإتمام هذه المعاملة'
  },
  [ERROR_CODES.TRANSFER_LIMIT_EXCEEDED]: {
    en: 'Transfer amount exceeds daily limit',
    ar: 'مبلغ التحويل يتجاوز الحد اليومي المسموح'
  },
  [ERROR_CODES.KYC_INCOMPLETE]: {
    en: 'Please complete your KYC verification',
    ar: 'يرجى إكمال التحقق من الهوية'
  },
  [ERROR_CODES.PAYMENT_FAILED]: {
    en: 'Payment processing failed',
    ar: 'فشل في معالجة الدفع'
  }
};

/**
 * Get localized error message
 * @param {string} code - Error code
 * @param {string} language - Language code (en/ar)
 * @returns {string} Localized error message
 */
const getErrorMessage = (code, language = 'en') => {
  const messages = ERROR_MESSAGES[code];
  if (!messages) {
    return language === 'ar' ? 'حدث خطأ غير متوقع' : 'An unexpected error occurred';
  }
  return messages[language] || messages.en;
};

/**
 * Create standardized error response
 * @param {Error} error - Error object
 * @param {string} language - Language preference
 * @returns {Object} Standardized error response
 */
const createErrorResponse = (error, language = 'en') => {
  const response = {
    success: false,
    error: {
      message: error.message,
      code: error.code || 'UNKNOWN_ERROR',
      statusCode: error.statusCode || 500,
      timestamp: error.timestamp || new Date().toISOString()
    }
  };

  // Add localized message if error code exists
  if (error.code && ERROR_MESSAGES[error.code]) {
    response.error.localizedMessage = getErrorMessage(error.code, language);
  }

  // Add details if available
  if (error.details) {
    response.error.details = error.details;
  }

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    response.error.stack = error.stack;
  }

  return response;
};

/**
 * Check if error is operational (expected) or programming error
 * @param {Error} error - Error object
 * @returns {boolean} True if operational error
 */
const isOperationalError = (error) => {
  if (error instanceof AppError) {
    return error.isOperational;
  }
  return false;
};

module.exports = {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  DatabaseError,
  ExternalServiceError,
  BusinessLogicError,
  SecurityError,
  PaymentError,
  KYCError,
  TransferError,
  ERROR_CODES,
  ERROR_MESSAGES,
  getErrorMessage,
  createErrorResponse,
  isOperationalError
};
