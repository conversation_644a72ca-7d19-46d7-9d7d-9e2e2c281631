{"name": "wistron-backend", "version": "1.0.0", "description": "Wistron Money Transfer System - Backend API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "babel src -d dist", "test": "jest --coverage", "test:watch": "jest --watch", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "seed": "knex seed:run", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "docs": "swagger-jsdoc -d swaggerDef.js src/routes/*.js -o docs/swagger.json"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "pg": "^8.11.3", "knex": "^3.0.1", "redis": "^4.6.10", "ioredis": "^5.3.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "axios": "^1.6.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "uuid": "^9.0.1", "crypto-js": "^4.2.0", "joi": "^17.11.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "express-winston": "^4.2.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "express-async-errors": "^3.1.1", "express-slow-down": "^2.0.1", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "node-cron": "^3.0.3", "pdf-lib": "^1.17.1", "html-pdf": "^3.0.1", "csv-parser": "^3.0.0", "fast-csv": "^4.3.6", "socket.io": "^4.7.4", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "passport-facebook": "^3.0.0"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["money-transfer", "fintech", "api", "nodejs", "express", "postgresql", "redis"], "author": "Wistron Development Team", "license": "PROPRIETARY", "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/*.js"]}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}}