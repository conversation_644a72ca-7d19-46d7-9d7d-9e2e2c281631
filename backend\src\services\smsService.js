/**
 * SMS Service using Twilio
 * خدمة الرسائل النصية باستخدام Twilio
 */

const twilio = require('twilio');
const logger = require('../utils/logger');

// Initialize Twilio client
const client = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

/**
 * Send verification SMS
 */
const sendVerificationSMS = async (phoneNumber, code, language = 'en') => {
  try {
    const messages = {
      en: `Your Wistron Money Transfer verification code is: ${code}. This code will expire in 10 minutes.`,
      ar: `رمز التحقق الخاص بك في ويسترون لتحويل الأموال هو: ${code}. ستنتهي صلاحية هذا الرمز خلال 10 دقائق.`
    };

    const message = messages[language] || messages.en;

    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phoneNumber
    });

    logger.info('Verification SMS sent successfully', {
      phoneNumber,
      messageSid: result.sid,
      language
    });

    return result;
  } catch (error) {
    logger.error('Failed to send verification SMS:', {
      phoneNumber,
      error: error.message,
      language
    });
    throw error;
  }
};

/**
 * Send transfer notification SMS
 */
const sendTransferNotificationSMS = async (phoneNumber, transferData, language = 'en') => {
  try {
    const messages = {
      en: `Wistron Transfer Update: Your transfer ${transferData.referenceNumber} to ${transferData.recipientName} for ${transferData.receiveAmount} ${transferData.receiveCurrency} is now ${transferData.status}. Track: ${transferData.trackingNumber}`,
      ar: `تحديث تحويل ويسترون: تحويلك ${transferData.referenceNumber} إلى ${transferData.recipientName} بمبلغ ${transferData.receiveAmount} ${transferData.receiveCurrency} أصبح ${transferData.status}. رقم التتبع: ${transferData.trackingNumber}`
    };

    const message = messages[language] || messages.en;

    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phoneNumber
    });

    logger.info('Transfer notification SMS sent successfully', {
      phoneNumber,
      transferId: transferData.id,
      messageSid: result.sid,
      language
    });

    return result;
  } catch (error) {
    logger.error('Failed to send transfer notification SMS:', {
      phoneNumber,
      transferId: transferData.id,
      error: error.message,
      language
    });
    throw error;
  }
};

/**
 * Send security alert SMS
 */
const sendSecurityAlertSMS = async (phoneNumber, alertType, language = 'en') => {
  try {
    const messages = {
      en: {
        login: 'Security Alert: New login to your Wistron account detected. If this wasn\'t you, please contact support immediately.',
        passwordChange: 'Security Alert: Your Wistron account password has been changed. If this wasn\'t you, please contact support immediately.',
        suspicious: 'Security Alert: Suspicious activity detected on your Wistron account. Please review your account and contact support if needed.'
      },
      ar: {
        login: 'تنبيه أمني: تم اكتشاف تسجيل دخول جديد إلى حساب ويسترون الخاص بك. إذا لم تكن أنت، يرجى الاتصال بالدعم فوراً.',
        passwordChange: 'تنبيه أمني: تم تغيير كلمة مرور حساب ويسترون الخاص بك. إذا لم تكن أنت، يرجى الاتصال بالدعم فوراً.',
        suspicious: 'تنبيه أمني: تم اكتشاف نشاط مشبوه على حساب ويسترون الخاص بك. يرجى مراجعة حسابك والاتصال بالدعم إذا لزم الأمر.'
      }
    };

    const message = messages[language]?.[alertType] || messages.en[alertType];

    if (!message) {
      throw new Error(`Unknown alert type: ${alertType}`);
    }

    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phoneNumber
    });

    logger.security('Security alert SMS sent', {
      phoneNumber,
      alertType,
      messageSid: result.sid,
      language
    });

    return result;
  } catch (error) {
    logger.error('Failed to send security alert SMS:', {
      phoneNumber,
      alertType,
      error: error.message,
      language
    });
    throw error;
  }
};

/**
 * Send OTP SMS
 */
const sendOTPSMS = async (phoneNumber, otp, purpose, language = 'en') => {
  try {
    const messages = {
      en: {
        transfer: `Your Wistron transfer OTP is: ${otp}. Use this code to authorize your transaction. Valid for 5 minutes.`,
        withdrawal: `Your Wistron withdrawal OTP is: ${otp}. Use this code to authorize your withdrawal. Valid for 5 minutes.`,
        general: `Your Wistron OTP is: ${otp}. Valid for 5 minutes.`
      },
      ar: {
        transfer: `رمز OTP الخاص بتحويل ويسترون هو: ${otp}. استخدم هذا الرمز لتأكيد معاملتك. صالح لمدة 5 دقائق.`,
        withdrawal: `رمز OTP الخاص بسحب ويسترون هو: ${otp}. استخدم هذا الرمز لتأكيد عملية السحب. صالح لمدة 5 دقائق.`,
        general: `رمز OTP الخاص بويسترون هو: ${otp}. صالح لمدة 5 دقائق.`
      }
    };

    const message = messages[language]?.[purpose] || messages[language]?.general || messages.en.general;

    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phoneNumber
    });

    logger.info('OTP SMS sent successfully', {
      phoneNumber,
      purpose,
      messageSid: result.sid,
      language
    });

    return result;
  } catch (error) {
    logger.error('Failed to send OTP SMS:', {
      phoneNumber,
      purpose,
      error: error.message,
      language
    });
    throw error;
  }
};

/**
 * Validate phone number format
 */
const validatePhoneNumber = (phoneNumber) => {
  // Basic international phone number validation
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phoneNumber);
};

/**
 * Format phone number for Twilio
 */
const formatPhoneNumber = (phoneNumber, countryCode = '+1') => {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // If it doesn't start with country code, add it
  if (!phoneNumber.startsWith('+')) {
    return `${countryCode}${cleaned}`;
  }
  
  return phoneNumber;
};

module.exports = {
  sendVerificationSMS,
  sendTransferNotificationSMS,
  sendSecurityAlertSMS,
  sendOTPSMS,
  validatePhoneNumber,
  formatPhoneNumber
};
