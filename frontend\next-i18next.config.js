/**
 * Next.js i18n Configuration
 * إعدادات الترجمة والتدويل
 */

module.exports = {
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'ar'],
    localeDetection: true,
    domains: [
      {
        domain: 'wistron.com',
        defaultLocale: 'en',
      },
      {
        domain: 'ar.wistron.com',
        defaultLocale: 'ar',
      },
    ],
  },
  fallbackLng: {
    'ar-SA': ['ar', 'en'],
    'ar-AE': ['ar', 'en'],
    'ar-EG': ['ar', 'en'],
    default: ['en'],
  },
  debug: process.env.NODE_ENV === 'development',
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  
  // Namespace configuration
  ns: [
    'common',
    'auth',
    'dashboard',
    'transfers',
    'profile',
    'admin',
    'errors',
    'validation'
  ],
  defaultNS: 'common',
  
  // Interpolation options
  interpolation: {
    escapeValue: false, // React already does escaping
    formatSeparator: ',',
    format: function(value, format, lng) {
      if (format === 'uppercase') return value.toUpperCase();
      if (format === 'lowercase') return value.toLowerCase();
      if (format === 'currency') {
        return new Intl.NumberFormat(lng, {
          style: 'currency',
          currency: 'USD'
        }).format(value);
      }
      if (format === 'date') {
        return new Intl.DateTimeFormat(lng).format(new Date(value));
      }
      return value;
    }
  },
  
  // React options
  react: {
    useSuspense: false,
    bindI18n: 'languageChanged',
    bindI18nStore: '',
    transEmptyNodeValue: '',
    transSupportBasicHtmlNodes: true,
    transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],
  },
  
  // Backend options for server-side
  backend: {
    loadPath: '/locales/{{lng}}/{{ns}}.json',
    addPath: '/locales/{{lng}}/{{ns}}.missing.json',
  },
  
  // Detection options
  detection: {
    order: ['querystring', 'cookie', 'localStorage', 'navigator', 'htmlTag'],
    caches: ['localStorage', 'cookie'],
    excludeCacheFor: ['cimode'],
    cookieMinutes: 160,
    cookieDomain: process.env.NODE_ENV === 'production' ? '.wistron.com' : 'localhost',
    cookieOptions: {
      path: '/',
      sameSite: 'strict',
      secure: process.env.NODE_ENV === 'production',
    },
  },
  
  // Load options
  load: 'languageOnly',
  preload: ['en', 'ar'],
  
  // Save missing keys
  saveMissing: process.env.NODE_ENV === 'development',
  saveMissingTo: 'fallback',
  
  // Pluralization
  pluralSeparator: '_',
  contextSeparator: '_',
  
  // Clean code options
  cleanCode: true,
  
  // Key separator
  keySeparator: '.',
  nsSeparator: ':',
  
  // Return objects
  returnObjects: false,
  returnedObjectHandler: function(key, value, options) {
    // Handle returned objects
  },
  
  // Post processing
  postProcess: false,
  
  // Misc options
  appendNamespaceToMissingKey: false,
  appendNamespaceToCIMode: false,
  
  // Experimental features
  experimental: {
    localeSubpaths: false,
  },
};
