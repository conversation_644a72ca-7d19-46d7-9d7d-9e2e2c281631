// Test Wistron API
const http = require('http');

function testAPI() {
  console.log('🧪 Testing Wistron Money Transfer API...\n');

  // Test 1: Health Check
  console.log('1. Testing Health Check...');
  const healthReq = http.request({
    hostname: 'localhost',
    port: 5000,
    path: '/health',
    method: 'GET'
  }, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      console.log('✅ Health Check:', JSON.parse(data).status);
      testLogin();
    });
  });
  
  healthReq.on('error', (err) => {
    console.log('❌ Health Check failed:', err.message);
  });
  
  healthReq.end();
}

function testLogin() {
  // Test 2: Login
  console.log('\n2. Testing Login...');
  const loginData = JSON.stringify({
    email: '<EMAIL>',
    password: 'Admin@123456'
  });

  const loginReq = http.request({
    hostname: 'localhost',
    port: 5000,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(loginData)
    }
  }, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (response.success) {
          console.log('✅ Login successful for:', response.data.user.email);
          console.log('   Role:', response.data.user.role);
          testRates();
        } else {
          console.log('❌ Login failed:', response.error);
        }
      } catch (e) {
        console.log('❌ Login response error:', e.message);
      }
    });
  });

  loginReq.on('error', (err) => {
    console.log('❌ Login request failed:', err.message);
  });

  loginReq.write(loginData);
  loginReq.end();
}

function testRates() {
  // Test 3: Exchange Rates
  console.log('\n3. Testing Exchange Rates...');
  const ratesReq = http.request({
    hostname: 'localhost',
    port: 5000,
    path: '/api/v1/rates',
    method: 'GET'
  }, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (response.success) {
          console.log('✅ Exchange rates retrieved:');
          console.log('   USD to SAR:', response.data.rates.SAR);
          console.log('   USD to AED:', response.data.rates.AED);
          console.log('   USD to EUR:', response.data.rates.EUR);
          showSummary();
        } else {
          console.log('❌ Rates failed:', response.error);
        }
      } catch (e) {
        console.log('❌ Rates response error:', e.message);
      }
    });
  });

  ratesReq.on('error', (err) => {
    console.log('❌ Rates request failed:', err.message);
  });

  ratesReq.end();
}

function showSummary() {
  console.log('\n🎉 ========================================');
  console.log('   Wistron Money Transfer System');
  console.log('   نظام ويسترون لتحويل الأموال');
  console.log('========================================');
  console.log('✅ النظام يعمل بنجاح!');
  console.log('');
  console.log('🌐 الروابط المهمة:');
  console.log('   الصفحة الرئيسية: http://localhost:5000');
  console.log('   توثيق API: http://localhost:5000/api-docs');
  console.log('   فحص الصحة: http://localhost:5000/health');
  console.log('');
  console.log('👤 حسابات التجربة:');
  console.log('   المدير: <EMAIL> / Admin@123456');
  console.log('   الوكيل: <EMAIL> / Agent@123456');
  console.log('');
  console.log('⚠️  ملاحظة: هذا خادم تجريبي مبسط');
  console.log('   للحصول على النظام الكامل، يرجى تثبيت Docker');
  console.log('========================================');
}

// Start testing
testAPI();
