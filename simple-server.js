// Simple Wistron Server
const http = require('http');

console.log('Starting Wistron Server...');

const server = http.createServer((req, res) => {
  const url = req.url;
  const method = req.method;
  
  console.log(`${method} ${url}`);
  
  // CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Routes
  if (url === '/' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <html>
      <head>
        <title>Wistron Money Transfer</title>
        <style>
          body { font-family: Arial; margin: 40px; background: #667eea; color: white; }
          .container { max-width: 800px; margin: 0 auto; text-align: center; }
          h1 { font-size: 3em; }
          .card { background: rgba(255,255,255,0.1); padding: 30px; margin: 20px; border-radius: 10px; }
          .btn { background: white; color: #667eea; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🏦 Wistron Money Transfer</h1>
          <div class="card">
            <h2>✅ النظام يعمل!</h2>
            <p>الخادم يعمل على المنفذ 5000</p>
            <a href="/health" class="btn">Health Check</a>
            <a href="/api/v1/rates" class="btn">Exchange Rates</a>
          </div>
          <div class="card">
            <h3>Test Accounts</h3>
            <p>Admin: <EMAIL> / Admin@123456</p>
            <p>Agent: <EMAIL> / Agent@123456</p>
          </div>
        </div>
      </body>
      </html>
    `);
    return;
  }
  
  if (url === '/health' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      status: 'OK',
      message: 'Wistron Server Running',
      timestamp: new Date().toISOString()
    }));
    return;
  }
  
  if (url === '/api/v1/rates' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: {
        base: 'USD',
        rates: { SAR: 3.75, AED: 3.67, EGP: 30.85, EUR: 0.85, GBP: 0.73 }
      }
    }));
    return;
  }
  
  if (url === '/api/v1/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        if (data.email === '<EMAIL>' && data.password === 'Admin@123456') {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: 'Login successful',
            data: { user: { email: data.email, role: 'admin' } }
          }));
        } else {
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: false, error: 'Invalid credentials' }));
        }
      } catch (e) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
      }
    });
    return;
  }
  
  // 404
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({
    success: false,
    error: 'Route not found',
    message: `المسار ${url} غير موجود`,
    availableRoutes: ['GET /', 'GET /health', 'GET /api/v1/rates', 'POST /api/v1/auth/login']
  }));
});

server.listen(5000, () => {
  console.log(`
🏦 Wistron Money Transfer Server
================================
✅ Server running on: http://localhost:5000
🏥 Health check: http://localhost:5000/health
💱 Exchange rates: http://localhost:5000/api/v1/rates

👤 Test login:
   Email: <EMAIL>
   Password: Admin@123456
================================`);
});

server.on('error', (err) => {
  console.error('Server error:', err.message);
  if (err.code === 'EADDRINUSE') {
    console.log('Port 5000 is already in use. Please close other servers first.');
  }
});
