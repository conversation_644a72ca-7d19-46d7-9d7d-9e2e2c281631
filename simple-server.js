// Simple Wistron Server
const http = require('http');

console.log('Starting Wistron Server...');

const server = http.createServer((req, res) => {
  const url = req.url;
  const method = req.method;
  
  console.log(`${method} ${url}`);
  
  // CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Routes
  if (url === '/' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <html>
      <head>
        <title>Wistron Money Transfer</title>
        <style>
          body { font-family: Arial; margin: 40px; background: #667eea; color: white; }
          .container { max-width: 800px; margin: 0 auto; text-align: center; }
          h1 { font-size: 3em; }
          .card { background: rgba(255,255,255,0.1); padding: 30px; margin: 20px; border-radius: 10px; }
          .btn { background: white; color: #667eea; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🏦 Wistron Money Transfer</h1>
          <div class="card">
            <h2>✅ النظام يعمل!</h2>
            <p>الخادم يعمل على المنفذ 5000</p>
            <a href="/health" class="btn">Health Check</a>
            <a href="/api/v1/rates" class="btn">Exchange Rates</a>
          </div>
          <div class="card">
            <h3>Test Accounts</h3>
            <p>Admin: <EMAIL> / Admin@123456</p>
            <p>Agent: <EMAIL> / Agent@123456</p>
          </div>
        </div>
      </body>
      </html>
    `);
    return;
  }
  
  if (url === '/health' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      status: 'OK',
      message: 'Wistron Server Running',
      timestamp: new Date().toISOString()
    }));
    return;
  }
  
  if (url === '/api/v1/rates' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      message: 'أسعار الصرف الحالية',
      data: {
        base: 'USD',
        rates: {
          SAR: 3.75,
          AED: 3.67,
          EGP: 30.85,
          JOD: 0.71,
          KWD: 0.31,
          QAR: 3.64,
          BHD: 0.38,
          OMR: 0.38,
          EUR: 0.85,
          GBP: 0.73
        },
        lastUpdated: new Date().toISOString()
      }
    }));
    return;
  }

  // Dashboard stats endpoint
  if (url === '/api/v1/dashboard/stats' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      message: 'إحصائيات النظام',
      data: {
        totalUsers: 2,
        activeUsers: 2,
        totalTransfers: 0,
        completedTransfers: 0,
        totalVolume: 0,
        todayTransfers: 0,
        todayVolume: 0,
        serverUptime: Math.floor(process.uptime())
      }
    }));
    return;
  }

  // User registration endpoint
  if (url === '/api/v1/auth/register' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('Registration attempt:', data.email);

        // Simple validation
        if (!data.email || !data.password || !data.firstName || !data.lastName) {
          res.writeHead(400, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: false,
            error: 'جميع الحقول مطلوبة',
            message: 'All fields are required'
          }));
          return;
        }

        // Check if user already exists (simple check)
        if (data.email === '<EMAIL>' || data.email === '<EMAIL>') {
          res.writeHead(409, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: false,
            error: 'البريد الإلكتروني مستخدم بالفعل',
            message: 'Email already exists'
          }));
          return;
        }

        // Simulate successful registration
        res.writeHead(201, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          message: 'تم إنشاء الحساب بنجاح',
          data: {
            user: {
              email: data.email,
              firstName: data.firstName,
              lastName: data.lastName,
              role: 'customer',
              status: 'active'
            }
          }
        }));
      } catch (e) {
        console.log('Registration JSON parse error:', e.message);
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          error: 'بيانات غير صحيحة',
          message: 'Invalid JSON format'
        }));
      }
    });
    return;
  }

  // List all available routes
  if (url === '/api/v1/routes' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      message: 'قائمة جميع المسارات المتاحة',
      data: {
        routes: [
          { method: 'GET', path: '/', description: 'الصفحة الرئيسية' },
          { method: 'GET', path: '/health', description: 'فحص صحة النظام' },
          { method: 'GET', path: '/api/v1/rates', description: 'أسعار الصرف' },
          { method: 'POST', path: '/api/v1/auth/login', description: 'تسجيل الدخول' },
          { method: 'POST', path: '/api/v1/auth/register', description: 'إنشاء حساب جديد' },
          { method: 'GET', path: '/api/v1/dashboard/stats', description: 'إحصائيات النظام' },
          { method: 'GET', path: '/api/v1/routes', description: 'قائمة المسارات' }
        ]
      }
    }));
    return;
  }
  
  if (url === '/api/v1/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('Login attempt:', data.email);

        // Check credentials
        if ((data.email === '<EMAIL>' && data.password === 'Admin@123456') ||
            (data.email === '<EMAIL>' && data.password === 'Agent@123456')) {

          const role = data.email.includes('admin') ? 'admin' : 'agent';

          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: 'تم تسجيل الدخول بنجاح',
            data: {
              user: {
                email: data.email,
                role: role,
                firstName: role === 'admin' ? 'System' : 'Sample',
                lastName: role === 'admin' ? 'Administrator' : 'Agent'
              },
              tokens: {
                accessToken: 'demo-token-' + Date.now(),
                refreshToken: 'demo-refresh-' + Date.now()
              }
            }
          }));
        } else {
          console.log('Invalid credentials for:', data.email);
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: false,
            error: 'بيانات الدخول غير صحيحة',
            message: 'Invalid email or password'
          }));
        }
      } catch (e) {
        console.log('JSON parse error:', e.message);
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          error: 'بيانات غير صحيحة',
          message: 'Invalid JSON format'
        }));
      }
    });
    return;
  }
  
  // 404 - Route not found
  console.log(`❌ Route not found: ${method} ${url}`);
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({
    success: false,
    error: 'المسار غير موجود',
    message: `المسار ${url} غير موجود`,
    requestedPath: url,
    requestedMethod: method,
    availableRoutes: [
      'GET /',
      'GET /health',
      'GET /api/v1/rates',
      'POST /api/v1/auth/login',
      'POST /api/v1/auth/register',
      'GET /api/v1/dashboard/stats',
      'GET /api/v1/routes'
    ],
    suggestion: 'تحقق من صحة المسار أو راجع /api/v1/routes للحصول على قائمة كاملة'
  }));
});

server.listen(5000, () => {
  console.log(`
🏦 Wistron Money Transfer Server
================================
✅ Server running on: http://localhost:5000
🏥 Health check: http://localhost:5000/health
💱 Exchange rates: http://localhost:5000/api/v1/rates

👤 Test login:
   Email: <EMAIL>
   Password: Admin@123456
================================`);
});

server.on('error', (err) => {
  console.error('Server error:', err.message);
  if (err.code === 'EADDRINUSE') {
    console.log('Port 5000 is already in use. Please close other servers first.');
  }
});
