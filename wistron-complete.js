/**
 * Wistron Money Transfer - Complete System
 * نظام ويسترون الكامل لتحويل الأموال
 */

console.log('🚀 Starting Wistron Money Transfer Complete System...');

// Try to load Express, install if not available
let express, cors;
try {
  express = require('express');
  cors = require('cors');
  console.log('✅ Express modules loaded successfully');
} catch (e) {
  console.log('📦 Installing required modules...');
  require('child_process').execSync('npm install express cors --no-save', { stdio: 'inherit' });
  express = require('express');
  cors = require('cors');
  console.log('✅ Express modules installed and loaded');
}

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} [${req.method}] ${req.path}`);
  next();
});

// Database simulation
const database = {
  users: [
    {
      id: '1', email: '<EMAIL>', password: 'Admin@123456',
      firstName: 'System', lastName: 'Administrator', role: 'super_admin',
      status: 'active', kycStatus: 'approved', createdAt: new Date().toISOString()
    },
    {
      id: '2', email: '<EMAIL>', password: 'Agent@123456',
      firstName: 'Sample', lastName: 'Agent', role: 'agent',
      status: 'active', kycStatus: 'approved', createdAt: new Date().toISOString()
    }
  ],
  transfers: [
    {
      id: '1', referenceNumber: 'WMT12345678', senderId: '1',
      senderName: 'John Doe', recipientName: 'Ahmed Ali',
      sendAmount: 1000, sendCurrency: 'USD', receiveAmount: 3750, receiveCurrency: 'SAR',
      exchangeRate: 3.75, fees: 15, status: 'completed',
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '2', referenceNumber: 'WMT87654321', senderId: '2',
      senderName: 'Sarah Johnson', recipientName: 'Fatima Hassan',
      sendAmount: 500, sendCurrency: 'USD', receiveAmount: 1835, receiveCurrency: 'AED',
      exchangeRate: 3.67, fees: 8, status: 'processing',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    }
  ],
  rates: { SAR: 3.75, AED: 3.67, EGP: 30.85, EUR: 0.85, GBP: 0.73, JOD: 0.71, KWD: 0.31 }
};

console.log('🛣️  Setting up routes...');

// Home page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>🏦 Wistron Money Transfer - Complete System</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 0; background: linear-gradient(135deg, #667eea, #764ba2); color: white; min-height: 100vh; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; }
        h1 { font-size: 3em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .status { background: rgba(76,175,80,0.2); border: 2px solid #4CAF50; border-radius: 10px; padding: 20px; margin: 20px 0; text-align: center; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .card { background: rgba(255,255,255,0.1); padding: 25px; border-radius: 10px; backdrop-filter: blur(10px); }
        .card h3 { color: #FFD700; margin-bottom: 15px; }
        .stat { display: flex; justify-content: space-between; margin: 8px 0; padding: 5px 0; border-bottom: 1px solid rgba(255,255,255,0.1); }
        .stat-value { font-weight: bold; color: #4CAF50; }
        .btn { display: inline-block; padding: 10px 20px; background: white; color: #667eea; text-decoration: none; border-radius: 5px; margin: 5px; font-weight: bold; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
        .endpoint { background: rgba(0,0,0,0.2); padding: 12px; margin: 8px 0; border-radius: 5px; font-family: monospace; }
        .method { color: #4CAF50; font-weight: bold; margin-right: 10px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🏦 Wistron Money Transfer</h1>
          <p>نظام ويسترون الكامل لتحويل الأموال - Complete System</p>
        </div>
        
        <div class="status">
          <h2>🚀 النظام الكامل يعمل بنجاح!</h2>
          <p><strong>✅ COMPLETE SYSTEM STATUS: OPERATIONAL</strong></p>
          <p>جميع الخدمات متاحة | All Services Available</p>
          <p>Server Uptime: ${Math.floor(process.uptime())} seconds</p>
        </div>
        
        <div class="grid">
          <div class="card">
            <h3>📊 System Statistics</h3>
            <div class="stat"><span>Total Users:</span><span class="stat-value">${database.users.length}</span></div>
            <div class="stat"><span>Total Transfers:</span><span class="stat-value">${database.transfers.length}</span></div>
            <div class="stat"><span>Completed:</span><span class="stat-value">${database.transfers.filter(t => t.status === 'completed').length}</span></div>
            <div class="stat"><span>Total Volume:</span><span class="stat-value">$${database.transfers.reduce((sum, t) => sum + t.sendAmount, 0).toLocaleString()}</span></div>
            <div class="stat"><span>Currencies:</span><span class="stat-value">${Object.keys(database.rates).length}</span></div>
          </div>
          
          <div class="card">
            <h3>👤 Test Accounts</h3>
            <p><strong>Super Admin:</strong><br>📧 <EMAIL><br>🔑 Admin@123456</p>
            <p><strong>Agent:</strong><br>📧 <EMAIL><br>🔑 Agent@123456</p>
            <div style="margin-top: 15px;">
              <a href="/health" class="btn">🏥 Health Check</a>
              <a href="/api-docs" class="btn">📚 API Docs</a>
            </div>
          </div>
          
          <div class="card">
            <h3>💱 Exchange Rates</h3>
            <div class="stat"><span>USD → SAR:</span><span class="stat-value">${database.rates.SAR}</span></div>
            <div class="stat"><span>USD → AED:</span><span class="stat-value">${database.rates.AED}</span></div>
            <div class="stat"><span>USD → EGP:</span><span class="stat-value">${database.rates.EGP}</span></div>
            <div class="stat"><span>USD → EUR:</span><span class="stat-value">${database.rates.EUR}</span></div>
            <div style="margin-top: 15px;">
              <a href="/api/v1/rates" class="btn">💱 Get Rates</a>
            </div>
          </div>
          
          <div class="card">
            <h3>🔧 API Endpoints</h3>
            <div class="endpoint"><span class="method">GET</span>/health</div>
            <div class="endpoint"><span class="method">POST</span>/api/v1/auth/login</div>
            <div class="endpoint"><span class="method">POST</span>/api/v1/auth/register</div>
            <div class="endpoint"><span class="method">GET</span>/api/v1/rates</div>
            <div class="endpoint"><span class="method">GET</span>/api/v1/dashboard/stats</div>
            <div class="endpoint"><span class="method">GET</span>/api/v1/transfers</div>
            <div class="endpoint"><span class="method">POST</span>/api/v1/transfers</div>
            <div class="endpoint"><span class="method">GET</span>/api/v1/users</div>
          </div>
        </div>
        
        <div class="card">
          <h3>📋 Recent Transfers</h3>
          ${database.transfers.map(t => `
            <div style="background: rgba(0,0,0,0.1); padding: 15px; margin: 10px 0; border-radius: 8px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                  <strong>${t.referenceNumber}</strong><br>
                  <small>${t.senderName} → ${t.recipientName}</small>
                </div>
                <div style="text-align: right;">
                  <div>${t.sendAmount} ${t.sendCurrency} → ${t.receiveAmount} ${t.receiveCurrency}</div>
                  <span style="background: ${t.status === 'completed' ? '#4CAF50' : '#FF9800'}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">${t.status}</span>
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    </body>
    </html>
  `);
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'OK',
    message: 'Wistron Money Transfer Complete System - All Services Operational',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0-complete',
    services: { database: 'connected', api: 'running', transfers: 'operational' },
    stats: {
      totalUsers: database.users.length,
      totalTransfers: database.transfers.length,
      completedTransfers: database.transfers.filter(t => t.status === 'completed').length
    }
  });
});

// Authentication
app.post('/api/v1/auth/login', (req, res) => {
  const { email, password } = req.body;
  console.log(`🔐 Login attempt: ${email}`);
  
  const user = database.users.find(u => u.email === email && u.password === password);
  if (!user) {
    return res.status(401).json({ success: false, error: 'Invalid credentials' });
  }
  
  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: { id: user.id, email: user.email, firstName: user.firstName, lastName: user.lastName, role: user.role },
      tokens: { accessToken: `wistron-token-${user.id}-${Date.now()}` }
    }
  });
});

app.post('/api/v1/auth/register', (req, res) => {
  const { email, password, firstName, lastName } = req.body;
  
  if (database.users.find(u => u.email === email)) {
    return res.status(409).json({ success: false, error: 'Email already exists' });
  }
  
  const newUser = {
    id: String(database.users.length + 1), email, password, firstName, lastName,
    role: 'customer', status: 'active', createdAt: new Date().toISOString()
  };
  
  database.users.push(newUser);
  res.status(201).json({
    success: true,
    message: 'Registration successful',
    data: { user: { id: newUser.id, email: newUser.email, firstName: newUser.firstName, lastName: newUser.lastName } }
  });
});

// Exchange rates
app.get('/api/v1/rates', (req, res) => {
  res.json({
    success: true,
    message: 'Current exchange rates',
    data: { base: 'USD', rates: database.rates, lastUpdated: new Date().toISOString() }
  });
});

// Dashboard stats
app.get('/api/v1/dashboard/stats', (req, res) => {
  const totalVolume = database.transfers.reduce((sum, t) => sum + t.sendAmount, 0);
  res.json({
    success: true,
    message: 'System statistics',
    data: {
      totalUsers: database.users.length,
      activeUsers: database.users.filter(u => u.status === 'active').length,
      totalTransfers: database.transfers.length,
      completedTransfers: database.transfers.filter(t => t.status === 'completed').length,
      totalVolume: totalVolume,
      averageAmount: totalVolume / database.transfers.length || 0,
      serverUptime: Math.floor(process.uptime())
    }
  });
});

// Transfers
app.get('/api/v1/transfers', (req, res) => {
  res.json({
    success: true,
    message: 'Transfers list',
    data: { transfers: database.transfers }
  });
});

app.post('/api/v1/transfers', (req, res) => {
  const { senderName, recipientName, sendAmount, sendCurrency, receiveCurrency } = req.body;
  
  const rate = database.rates[receiveCurrency] || 1;
  const receiveAmount = Math.round(sendAmount * rate * 100) / 100;
  const fees = Math.max(5, sendAmount * 0.015);
  
  const newTransfer = {
    id: String(database.transfers.length + 1),
    referenceNumber: 'WMT' + Math.random().toString(36).substr(2, 8).toUpperCase(),
    senderId: '1', senderName, recipientName,
    sendAmount: parseFloat(sendAmount), sendCurrency, receiveAmount, receiveCurrency,
    exchangeRate: rate, fees: Math.round(fees * 100) / 100,
    status: 'pending', createdAt: new Date().toISOString()
  };
  
  database.transfers.push(newTransfer);
  res.status(201).json({
    success: true,
    message: 'Transfer created successfully',
    data: { transfer: newTransfer }
  });
});

// Users
app.get('/api/v1/users', (req, res) => {
  const users = database.users.map(u => ({
    id: u.id, email: u.email, firstName: u.firstName, lastName: u.lastName,
    role: u.role, status: u.status, createdAt: u.createdAt
  }));
  res.json({ success: true, message: 'Users list', data: { users } });
});

// API Documentation
app.get('/api-docs', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Wistron API Documentation</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
        h1 { color: #667eea; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
        .endpoint { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #667eea; }
        .method { background: #007bff; color: white; padding: 5px 15px; border-radius: 20px; margin-right: 10px; }
        pre { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🏦 Wistron Money Transfer - Complete API</h1>
        
        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>👤 Test Accounts</h3>
          <p><strong>Admin:</strong> <EMAIL> / Admin@123456</p>
          <p><strong>Agent:</strong> <EMAIL> / Agent@123456</p>
        </div>
        
        <div class="endpoint">
          <span class="method">GET</span><strong>/health</strong>
          <p>Complete system health check</p>
        </div>
        
        <div class="endpoint">
          <span class="method">POST</span><strong>/api/v1/auth/login</strong>
          <p>User authentication</p>
          <pre>{"email": "<EMAIL>", "password": "Admin@123456"}</pre>
        </div>
        
        <div class="endpoint">
          <span class="method">POST</span><strong>/api/v1/auth/register</strong>
          <p>User registration</p>
          <pre>{"email": "<EMAIL>", "password": "Password@123", "firstName": "John", "lastName": "Doe"}</pre>
        </div>
        
        <div class="endpoint">
          <span class="method">GET</span><strong>/api/v1/rates</strong>
          <p>Exchange rates</p>
        </div>
        
        <div class="endpoint">
          <span class="method">GET</span><strong>/api/v1/dashboard/stats</strong>
          <p>System statistics</p>
        </div>
        
        <div class="endpoint">
          <span class="method">GET</span><strong>/api/v1/transfers</strong>
          <p>List transfers</p>
        </div>
        
        <div class="endpoint">
          <span class="method">POST</span><strong>/api/v1/transfers</strong>
          <p>Create transfer</p>
          <pre>{"senderName": "John Doe", "recipientName": "Ahmed Ali", "sendAmount": 1000, "sendCurrency": "USD", "receiveCurrency": "SAR"}</pre>
        </div>
        
        <div class="endpoint">
          <span class="method">GET</span><strong>/api/v1/users</strong>
          <p>List users</p>
        </div>
      </div>
    </body>
    </html>
  `);
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    availableRoutes: ['GET /', 'GET /health', 'POST /api/v1/auth/login', 'GET /api/v1/rates', 'GET /api/v1/transfers']
  });
});

// Start server
const PORT = 5000;
app.listen(PORT, () => {
  console.log(`
🏦 ========================================
   WISTRON MONEY TRANSFER - COMPLETE SYSTEM
   نظام ويسترون الكامل لتحويل الأموال
========================================

🚀 Complete System: http://localhost:${PORT}
📚 API Documentation: http://localhost:${PORT}/api-docs
🏥 Health Check: http://localhost:${PORT}/health

👤 Test Accounts:
   Admin: <EMAIL> / Admin@123456
   Agent: <EMAIL> / Agent@123456

📊 System Stats:
   Users: ${database.users.length}
   Transfers: ${database.transfers.length}
   Currencies: ${Object.keys(database.rates).length}

✅ ALL SYSTEMS OPERATIONAL!

========================================`);
});

module.exports = { app, database };
