/**
 * Wistron Money Transfer - Demo Server
 * خادم تجريبي لنظام ويسترون لتحويل الأموال
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Simple in-memory data
const users = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'Admin@123456',
    firstName: 'System',
    lastName: 'Administrator',
    role: 'super_admin',
    status: 'active'
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'Agent@123456',
    firstName: 'Sample',
    lastName: 'Agent',
    role: 'agent',
    status: 'active'
  }
];

// Routes
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>🏦 Wistron Money Transfer</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 40px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
          }
          .container { max-width: 800px; margin: 0 auto; text-align: center; }
          h1 { font-size: 3em; margin-bottom: 20px; }
          .subtitle { font-size: 1.2em; margin-bottom: 40px; opacity: 0.9; }
          .card { 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            margin: 20px 0;
            backdrop-filter: blur(10px);
          }
          .btn { 
            display: inline-block;
            padding: 15px 30px; 
            background: white; 
            color: #667eea; 
            text-decoration: none; 
            border-radius: 8px; 
            margin: 10px;
            font-weight: bold;
            transition: transform 0.2s;
          }
          .btn:hover { transform: translateY(-2px); }
          .status { color: #4ade80; font-weight: bold; }
          .endpoint { 
            background: rgba(0,0,0,0.2); 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px; 
            text-align: left;
          }
          .method { color: #4ade80; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🏦 Wistron Money Transfer</h1>
          <p class="subtitle">نظام ويسترون المتكامل لتحويل الأموال</p>
          
          <div class="card">
            <h2>🚀 النظام يعمل بنجاح!</h2>
            <p class="status">✅ Server Status: RUNNING</p>
            <p>الخادم التجريبي يعمل على المنفذ ${process.env.PORT || 5000}</p>
          </div>
          
          <div class="card">
            <h3>🔗 الروابط المهمة</h3>
            <a href="/api-docs" class="btn">📚 توثيق API</a>
            <a href="/health" class="btn">🏥 فحص الصحة</a>
            <a href="/api/v1/rates" class="btn">💱 أسعار الصرف</a>
          </div>
          
          <div class="card">
            <h3>👤 حسابات التجربة</h3>
            <p><strong>المدير:</strong> <EMAIL> / Admin@123456</p>
            <p><strong>الوكيل:</strong> <EMAIL> / Agent@123456</p>
          </div>
          
          <div class="card">
            <h3>🔧 API Endpoints</h3>
            <div class="endpoint">
              <span class="method">POST</span> /api/v1/auth/login - تسجيل الدخول
            </div>
            <div class="endpoint">
              <span class="method">POST</span> /api/v1/auth/register - إنشاء حساب
            </div>
            <div class="endpoint">
              <span class="method">GET</span> /api/v1/rates - أسعار الصرف
            </div>
            <div class="endpoint">
              <span class="method">GET</span> /api/v1/dashboard/stats - إحصائيات
            </div>
          </div>
          
          <div class="card">
            <h3>⚠️ ملاحظة مهمة</h3>
            <p>هذا خادم تجريبي يستخدم الذاكرة المؤقتة</p>
            <p>البيانات ستفقد عند إعادة التشغيل</p>
            <p>للحصول على نظام كامل، يرجى تثبيت Docker</p>
          </div>
        </div>
      </body>
    </html>
  `);
});

app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Wistron Money Transfer Demo Server',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0-demo'
  });
});

app.post('/api/v1/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  console.log('🔐 Login attempt:', email);
  
  const user = users.find(u => u.email === email && u.password === password);
  
  if (!user) {
    return res.status(401).json({
      success: false,
      error: 'Invalid credentials'
    });
  }
  
  const token = Buffer.from(\`\${user.id}:\${Date.now()}\`).toString('base64');
  
  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status
      },
      tokens: {
        accessToken: token,
        refreshToken: token
      }
    }
  });
});

app.post('/api/v1/auth/register', (req, res) => {
  const { email, password, firstName, lastName } = req.body;
  
  console.log('📝 Registration attempt:', email);
  
  if (users.find(u => u.email === email)) {
    return res.status(409).json({
      success: false,
      error: 'Email already registered'
    });
  }
  
  const newUser = {
    id: String(users.length + 1),
    email,
    password,
    firstName,
    lastName,
    role: 'customer',
    status: 'active'
  };
  
  users.push(newUser);
  
  res.status(201).json({
    success: true,
    message: 'Registration successful',
    data: {
      user: {
        id: newUser.id,
        email: newUser.email,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        status: newUser.status
      }
    }
  });
});

app.get('/api/v1/rates', (req, res) => {
  res.json({
    success: true,
    data: {
      base: 'USD',
      rates: {
        SAR: 3.75,
        AED: 3.67,
        EGP: 30.85,
        JOD: 0.71,
        KWD: 0.31,
        QAR: 3.64,
        BHD: 0.38,
        OMR: 0.38,
        EUR: 0.85,
        GBP: 0.73
      },
      lastUpdated: new Date().toISOString()
    }
  });
});

app.get('/api/v1/dashboard/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalUsers: users.length,
      activeUsers: users.filter(u => u.status === 'active').length,
      totalTransfers: 0,
      completedTransfers: 0,
      totalVolume: 0,
      todayTransfers: 0,
      todayVolume: 0
    }
  });
});

app.get('/api-docs', (req, res) => {
  res.send(\`
    <html>
      <head>
        <title>Wistron API Documentation</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
          .container { max-width: 1000px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
          h1 { color: #667eea; border-bottom: 3px solid #667eea; padding-bottom: 10px; }
          .endpoint { 
            background: #f8f9fa; 
            padding: 20px; 
            margin: 15px 0; 
            border-radius: 8px; 
            border-left: 4px solid #667eea;
          }
          .method { 
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
          }
          .get { background: #28a745; }
          .post { background: #007bff; }
          .put { background: #ffc107; color: #000; }
          .delete { background: #dc3545; }
          pre { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto; }
          .accounts { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🏦 Wistron Money Transfer API</h1>
          <p>توثيق API لنظام ويسترون لتحويل الأموال - الإصدار التجريبي</p>
          
          <div class="accounts">
            <h3>👤 حسابات التجربة</h3>
            <ul>
              <li><strong>المدير:</strong> <EMAIL> / Admin@123456</li>
              <li><strong>الوكيل:</strong> <EMAIL> / Agent@123456</li>
            </ul>
          </div>
          
          <div class="endpoint">
            <span class="method get">GET</span>
            <strong>/health</strong>
            <p>فحص صحة النظام</p>
          </div>
          
          <div class="endpoint">
            <span class="method post">POST</span>
            <strong>/api/v1/auth/login</strong>
            <p>تسجيل دخول المستخدم</p>
            <pre>{\n  "email": "<EMAIL>",\n  "password": "Admin@123456"\n}</pre>
          </div>
          
          <div class="endpoint">
            <span class="method post">POST</span>
            <strong>/api/v1/auth/register</strong>
            <p>تسجيل مستخدم جديد</p>
            <pre>{\n  "email": "<EMAIL>",\n  "password": "Password@123",\n  "firstName": "John",\n  "lastName": "Doe"\n}</pre>
          </div>
          
          <div class="endpoint">
            <span class="method get">GET</span>
            <strong>/api/v1/rates</strong>
            <p>الحصول على أسعار الصرف الحالية</p>
          </div>
          
          <div class="endpoint">
            <span class="method get">GET</span>
            <strong>/api/v1/dashboard/stats</strong>
            <p>إحصائيات لوحة التحكم</p>
          </div>
          
          <p style="margin-top: 40px; text-align: center; color: #666;">
            🚀 Wistron Money Transfer Demo Server - Port \${process.env.PORT || 5000}
          </p>
        </div>
      </body>
    </html>
  \`);
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    message: \`The requested route \${req.originalUrl} does not exist.\`
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(\`
🏦 ========================================
   Wistron Money Transfer Demo Server
   نظام ويسترون لتحويل الأموال
========================================

🚀 Server running on: http://localhost:\${PORT}
📚 API Documentation: http://localhost:\${PORT}/api-docs
🏥 Health Check: http://localhost:\${PORT}/health
💱 Exchange Rates: http://localhost:\${PORT}/api/v1/rates

👤 Test Accounts:
   Admin: <EMAIL> / Admin@123456
   Agent: <EMAIL> / Agent@123456

⚠️  Note: Demo server using in-memory storage
   Data will be lost on restart

========================================\`);
});

module.exports = app;
