@echo off
REM Wistron Money Transfer System - Quick Start Script for Windows
REM سكريبت التشغيل السريع لنظام ويسترون لتحويل الأموال - ويندوز

setlocal enabledelayedexpansion

REM Colors (limited support in Windows)
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

REM Function to print status messages
:print_status
echo %GREEN%[INFO]%NC% %~1
goto :eof

:print_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:print_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:print_header
echo %BLUE%================================%NC%
echo %BLUE%%~1%NC%
echo %BLUE%================================%NC%
goto :eof

REM Check if Docker is installed
:check_docker
docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not installed. Please install Docker Desktop first."
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose is not installed. Please install Docker Desktop first."
    pause
    exit /b 1
)

call :print_status "Docker and Docker Compose are installed ✓"
goto :eof

REM Check if Node.js is installed
:check_nodejs
node --version >nul 2>&1
if errorlevel 1 (
    call :print_warning "Node.js is not installed. Docker mode will be used."
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    call :print_status "Node.js version: !NODE_VERSION! ✓"
)
goto :eof

REM Setup environment files
:setup_env_files
call :print_status "Setting up environment files..."

REM Backend .env
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env" >nul
    call :print_status "Created backend\.env from example"
) else (
    call :print_warning "backend\.env already exists, skipping..."
)

REM Frontend .env.local
if not exist "frontend\.env.local" (
    (
        echo NEXT_PUBLIC_API_URL=http://localhost:5000/api/v1
        echo NEXT_PUBLIC_APP_NAME=Wistron Money Transfer
        echo NEXT_PUBLIC_APP_VERSION=1.0.0
        echo NEXT_PUBLIC_SOCKET_URL=http://localhost:5000
        echo NEXTAUTH_URL=http://localhost:3000
        echo NEXTAUTH_SECRET=wistron_nextauth_secret_2024
    ) > "frontend\.env.local"
    call :print_status "Created frontend\.env.local"
) else (
    call :print_warning "frontend\.env.local already exists, skipping..."
)
goto :eof

REM Start services with Docker
:start_docker
call :print_header "Starting Wistron Money Transfer with Docker"

call :print_status "Building and starting all services..."
docker-compose up -d --build

call :print_status "Waiting for services to be ready..."
timeout /t 10 /nobreak >nul

REM Check if services are running
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    call :print_error "Failed to start services. Check docker-compose logs for details."
    docker-compose logs
    pause
    exit /b 1
)

call :print_status "Services are starting up..."

REM Wait for database to be ready
call :print_status "Waiting for database to be ready..."
:wait_db
docker-compose exec -T postgres pg_isready -U wistron_user -d wistron_money_transfer >nul 2>&1
if errorlevel 1 (
    timeout /t 2 /nobreak >nul
    goto wait_db
)

call :print_status "Database is ready ✓"

REM Run migrations
call :print_status "Running database migrations..."
docker-compose exec -T backend npm run migrate

REM Seed database
call :print_status "Seeding database with initial data..."
docker-compose exec -T backend npm run seed

call :print_header "🚀 Wistron Money Transfer is now running!"
echo.
echo %GREEN%Frontend:%NC% http://localhost:3000
echo %GREEN%Backend API:%NC% http://localhost:5000
echo %GREEN%API Documentation:%NC% http://localhost:5000/api-docs
echo %GREEN%Grafana Dashboard:%NC% http://localhost:3001 (admin/wistron_grafana_admin_2024)
echo %GREEN%Prometheus:%NC% http://localhost:9090
echo.
echo %BLUE%Test Accounts:%NC%
echo Admin: <EMAIL> / Admin@123456
echo Agent: <EMAIL> / Agent@123456
echo.
echo %YELLOW%To stop the services:%NC% docker-compose down
echo %YELLOW%To view logs:%NC% docker-compose logs -f
echo.
pause
goto :eof

REM Start services locally
:start_local
call :print_header "Starting Wistron Money Transfer in Development Mode"

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Node.js is required for local development. Please install Node.js 18+ or use Docker mode."
    pause
    exit /b 1
)

REM Start database and Redis with Docker
call :print_status "Starting database and Redis..."
docker-compose up -d postgres redis

REM Wait for database
call :print_status "Waiting for database to be ready..."
:wait_db_local
docker-compose exec -T postgres pg_isready -U wistron_user -d wistron_money_transfer >nul 2>&1
if errorlevel 1 (
    timeout /t 2 /nobreak >nul
    goto wait_db_local
)

REM Install dependencies if needed
if not exist "backend\node_modules" (
    call :print_status "Installing backend dependencies..."
    cd backend && npm install && cd ..
)

if not exist "frontend\node_modules" (
    call :print_status "Installing frontend dependencies..."
    cd frontend && npm install && cd ..
)

REM Run migrations
call :print_status "Running database migrations..."
cd backend && npm run migrate && cd ..

REM Seed database
call :print_status "Seeding database..."
cd backend && npm run seed && cd ..

call :print_header "🚀 Starting development servers..."

REM Start backend and frontend
start "Wistron Backend" cmd /k "cd backend && npm run dev"
start "Wistron Frontend" cmd /k "cd frontend && npm run dev"

timeout /t 5 /nobreak >nul

call :print_header "✅ Development servers are running!"
echo.
echo %GREEN%Frontend:%NC% http://localhost:3000
echo %GREEN%Backend API:%NC% http://localhost:5000
echo %GREEN%API Documentation:%NC% http://localhost:5000/api-docs
echo.
echo %BLUE%Test Accounts:%NC%
echo Admin: <EMAIL> / Admin@123456
echo Agent: <EMAIL> / Agent@123456
echo.
echo %YELLOW%Close the terminal windows to stop the services%NC%
echo.
pause
goto :eof

REM Health check
:health_check
call :print_header "Running Health Check"

curl -s http://localhost:5000/health >nul 2>&1
if errorlevel 1 (
    call :print_error "Backend API is not responding ✗"
) else (
    call :print_status "Backend API is healthy ✓"
)

curl -s http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    call :print_error "Frontend is not responding ✗"
) else (
    call :print_status "Frontend is healthy ✓"
)
pause
goto :eof

REM Stop services
:stop_services
call :print_header "Stopping Wistron Money Transfer Services"

call :print_status "Stopping Docker services..."
docker-compose down

call :print_status "All services stopped ✓"
pause
goto :eof

REM Show help
:show_help
echo Wistron Money Transfer - Quick Start Script
echo.
echo Usage: %~nx0 [OPTION]
echo.
echo Options:
echo   simple    Start simple demo server (default, no Docker needed)
echo   docker    Start with Docker (full system)
echo   local     Start in local development mode
echo   health    Run health check
echo   stop      Stop all services
echo   help      Show this help message
echo.
echo Examples:
echo   %~nx0 simple   # Start simple demo (recommended for testing)
echo   %~nx0 docker   # Start with Docker (full system)
echo   %~nx0 local    # Start in development mode
echo   %~nx0 health   # Check service health
echo.
echo Note: 'simple' mode requires only Node.js and runs a demo server
echo       'docker' mode requires Docker and provides full functionality
pause
goto :eof

REM Main script logic
:main
call :print_header "🏦 Wistron Money Transfer System"
call :print_status "نظام ويسترون المتكامل لتحويل الأموال"
echo.

REM Check prerequisites
call :check_docker
call :check_nodejs

REM Setup environment files
call :setup_env_files

REM Start simple demo server
:start_simple
call :print_header "Starting Wistron Money Transfer - Simple Demo"

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Node.js is required. Please install Node.js 18+ first."
    pause
    exit /b 1
)

REM Kill any existing Node processes
call :print_status "Stopping any existing servers..."
taskkill /F /IM node.exe >nul 2>&1

REM Start the simple server
call :print_status "Starting Wistron demo server..."
start "Wistron Demo Server" /MIN node simple-server.js

REM Wait for server to start
timeout /t 3 /nobreak >nul

REM Test if server is running
curl -s http://localhost:5000/health >nul 2>&1
if errorlevel 1 (
    call :print_error "Failed to start server. Check for port conflicts."
    pause
    exit /b 1
)

call :print_header "🚀 Wistron Money Transfer Demo is running!"
echo.
echo %GREEN%✅ Server Status: RUNNING%NC%
echo %GREEN%🌐 Homepage: http://localhost:5000%NC%
echo %GREEN%🏥 Health Check: http://localhost:5000/health%NC%
echo %GREEN%💱 Exchange Rates: http://localhost:5000/api/v1/rates%NC%
echo.
echo %BLUE%👤 Test Accounts:%NC%
echo    Admin: <EMAIL> / Admin@123456
echo    Agent: <EMAIL> / Agent@123456
echo.
echo %YELLOW%📝 Available API Endpoints:%NC%
echo    GET  /health
echo    GET  /api/v1/rates
echo    POST /api/v1/auth/login
echo.
echo %YELLOW%⚠️  Note: This is a demo server with in-memory storage%NC%
echo %YELLOW%   For full system, please install Docker%NC%
echo.
echo Opening browser...
start http://localhost:5000
echo.
echo Press any key to stop the server...
pause >nul

REM Stop the server
taskkill /F /IM node.exe >nul 2>&1
call :print_status "Server stopped"
goto :eof

REM Handle command line arguments
set "action=%~1"
if "%action%"=="" set "action=simple"

if "%action%"=="simple" (
    call :start_simple
) else if "%action%"=="docker" (
    call :start_docker
) else if "%action%"=="local" (
    call :start_local
) else if "%action%"=="health" (
    call :health_check
) else if "%action%"=="stop" (
    call :stop_services
) else if "%action%"=="help" (
    call :show_help
) else (
    call :print_error "Unknown option: %action%"
    call :show_help
    exit /b 1
)

goto :eof

REM Run main function
call :main %*
