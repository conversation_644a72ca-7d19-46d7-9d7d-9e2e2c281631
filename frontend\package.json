{"name": "wistron-frontend", "version": "1.0.0", "description": "Wistron Money Transfer System - Frontend Web Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "axios": "^1.6.2", "swr": "^2.2.4", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "react-dropzone": "^14.2.3", "react-qr-code": "^2.0.12", "qrcode.js": "^0.0.2", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.294.0", "react-intersection-observer": "^9.5.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "react-helmet-async": "^2.0.4", "next-themes": "^0.2.1", "next-auth": "^4.24.5", "next-i18next": "^15.2.0", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "socket.io-client": "^4.7.4", "react-use": "^17.4.2", "react-copy-to-clipboard": "^5.1.0", "react-otp-input": "^3.1.1", "react-phone-input-2": "^2.15.1", "country-list": "^2.3.0", "currency-list": "^1.0.10", "react-currency-input-field": "^3.6.11", "react-signature-canvas": "^1.0.6"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/react-window": "^1.8.8", "@types/react-datepicker": "^4.19.4", "@types/react-copy-to-clipboard": "^5.0.7", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "@storybook/addon-essentials": "^7.6.3", "@storybook/addon-interactions": "^7.6.3", "@storybook/addon-links": "^7.6.3", "@storybook/blocks": "^7.6.3", "@storybook/nextjs": "^7.6.3", "@storybook/react": "^7.6.3", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.3", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/**/*.stories.{js,jsx,ts,tsx}"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm test"}}}