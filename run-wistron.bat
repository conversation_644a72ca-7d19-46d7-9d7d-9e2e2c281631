@echo off
echo ========================================
echo 🏦 Wistron Money Transfer System
echo نظام ويسترون المتكامل لتحويل الأموال
echo ========================================
echo.

echo [INFO] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed!
    echo Please install Node.js from: https://nodejs.org
    pause
    exit /b 1
)

echo [INFO] Node.js is available ✓
echo.

echo [INFO] Stopping any existing servers...
taskkill /F /IM node.exe >nul 2>&1

echo [INFO] Starting Wistron Money Transfer Server...
echo.

start "Wistron Server" node simple-server.js

echo Waiting for server to start...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo ✅ Wistron Money Transfer is Running!
echo ========================================
echo.
echo 🌐 Open these links in your browser:
echo    Homepage: http://localhost:5000
echo    Health Check: http://localhost:5000/health
echo    Exchange Rates: http://localhost:5000/api/v1/rates
echo.
echo 👤 Test Accounts:
echo    Admin: <EMAIL> / Admin@123456
echo    Agent: <EMAIL> / Agent@123456
echo.
echo 🔧 API Test Commands:
echo    curl http://localhost:5000/health
echo    curl http://localhost:5000/api/v1/rates
echo.
echo ⚠️  Note: Demo server with in-memory storage
echo    Data will be lost when server stops
echo.
echo Opening browser...
start http://localhost:5000
echo.
echo ========================================
echo Press any key to stop the server...
pause >nul

echo.
echo [INFO] Stopping server...
taskkill /F /IM node.exe >nul 2>&1
echo [INFO] Server stopped ✓
echo.
echo Thank you for using Wistron Money Transfer!
pause
