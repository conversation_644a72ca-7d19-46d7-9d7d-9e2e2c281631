@echo off
echo ========================================
echo 🏦 Wistron Money Transfer System
echo نظام ويسترون المتكامل لتحويل الأموال
echo ========================================
echo.

echo [INFO] تحقق من Node.js...
node --version
if errorlevel 1 (
    echo [ERROR] Node.js غير مثبت. يرجى تثبيت Node.js 18+ أولاً
    pause
    exit /b 1
)

echo [INFO] Node.js متوفر ✓
echo.

echo [INFO] تثبيت تبعيات Backend...
cd backend
if not exist node_modules (
    echo تثبيت تبعيات Backend...
    call npm install
    if errorlevel 1 (
        echo [ERROR] فشل في تثبيت تبعيات Backend
        pause
        exit /b 1
    )
)
cd ..

echo [INFO] تثبيت تبعيات Frontend...
cd frontend
if not exist node_modules (
    echo تثبيت تبعيات Frontend...
    call npm install
    if errorlevel 1 (
        echo [ERROR] فشل في تثبيت تبعيات Frontend
        pause
        exit /b 1
    )
)
cd ..

echo.
echo ========================================
echo 🚀 تشغيل النظام...
echo ========================================
echo.

echo [INFO] تشغيل Backend على المنفذ 5000...
start "Wistron Backend" cmd /k "cd backend && npm run dev:simple"

echo [INFO] انتظار 5 ثوان لتشغيل Backend...
timeout /t 5 /nobreak >nul

echo [INFO] تشغيل Frontend على المنفذ 3000...
start "Wistron Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo ========================================
echo ✅ النظام يعمل الآن!
echo ========================================
echo.
echo 🌐 الواجهة الرئيسية: http://localhost:3000
echo 🔧 Backend API: http://localhost:5000
echo 📚 توثيق API: http://localhost:5000/api-docs
echo.
echo 👤 حسابات التجربة:
echo    Admin: <EMAIL> / Admin@123456
echo    Agent: <EMAIL> / Agent@123456
echo.
echo ⚠️  ملاحظة: قاعدة البيانات تعمل في الذاكرة فقط
echo    للحصول على قاعدة بيانات دائمة، يرجى تثبيت Docker
echo.
echo اضغط أي مفتاح للمتابعة...
pause >nul

echo.
echo فتح المتصفح...
start http://localhost:3000

echo.
echo النظام يعمل! أغلق نوافذ Terminal لإيقاف الخدمات
pause
