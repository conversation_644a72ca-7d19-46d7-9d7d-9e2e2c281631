/**
 * Email Service
 * خدمة البريد الإلكتروني
 */

const nodemailer = require('nodemailer');
const logger = require('../utils/logger');

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    },
    tls: {
      rejectUnauthorized: false
    }
  });
};

/**
 * Send email verification
 */
const sendVerificationEmail = async (email, token, language = 'en') => {
  try {
    const transporter = createTransporter();
    
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
    
    const templates = {
      en: {
        subject: 'Verify Your Email - Wistron Money Transfer',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
              <h1 style="color: white; margin: 0;">Wistron Money Transfer</h1>
            </div>
            <div style="padding: 30px; background: #f9f9f9;">
              <h2 style="color: #333;">Verify Your Email Address</h2>
              <p style="color: #666; line-height: 1.6;">
                Thank you for registering with Wistron Money Transfer. Please click the button below to verify your email address.
              </p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${verificationUrl}" 
                   style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                  Verify Email
                </a>
              </div>
              <p style="color: #999; font-size: 14px;">
                If you didn't create an account, please ignore this email.
              </p>
              <p style="color: #999; font-size: 14px;">
                This link will expire in 24 hours.
              </p>
            </div>
          </div>
        `
      },
      ar: {
        subject: 'تأكيد البريد الإلكتروني - ويسترون لتحويل الأموال',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; direction: rtl;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
              <h1 style="color: white; margin: 0;">ويسترون لتحويل الأموال</h1>
            </div>
            <div style="padding: 30px; background: #f9f9f9;">
              <h2 style="color: #333;">تأكيد عنوان البريد الإلكتروني</h2>
              <p style="color: #666; line-height: 1.6;">
                شكراً لك على التسجيل في ويسترون لتحويل الأموال. يرجى النقر على الزر أدناه لتأكيد عنوان بريدك الإلكتروني.
              </p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${verificationUrl}" 
                   style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                  تأكيد البريد الإلكتروني
                </a>
              </div>
              <p style="color: #999; font-size: 14px;">
                إذا لم تقم بإنشاء حساب، يرجى تجاهل هذا البريد الإلكتروني.
              </p>
              <p style="color: #999; font-size: 14px;">
                ستنتهي صلاحية هذا الرابط خلال 24 ساعة.
              </p>
            </div>
          </div>
        `
      }
    };

    const template = templates[language] || templates.en;

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: email,
      subject: template.subject,
      html: template.html
    };

    const result = await transporter.sendMail(mailOptions);
    
    logger.info('Verification email sent successfully', {
      email,
      messageId: result.messageId,
      language
    });

    return result;
  } catch (error) {
    logger.error('Failed to send verification email:', {
      email,
      error: error.message,
      language
    });
    throw error;
  }
};

/**
 * Send password reset email
 */
const sendPasswordResetEmail = async (email, token, language = 'en') => {
  try {
    const transporter = createTransporter();
    
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
    
    const templates = {
      en: {
        subject: 'Password Reset - Wistron Money Transfer',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
              <h1 style="color: white; margin: 0;">Wistron Money Transfer</h1>
            </div>
            <div style="padding: 30px; background: #f9f9f9;">
              <h2 style="color: #333;">Reset Your Password</h2>
              <p style="color: #666; line-height: 1.6;">
                You requested a password reset for your Wistron Money Transfer account. Click the button below to reset your password.
              </p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}" 
                   style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                  Reset Password
                </a>
              </div>
              <p style="color: #999; font-size: 14px;">
                If you didn't request this reset, please ignore this email.
              </p>
              <p style="color: #999; font-size: 14px;">
                This link will expire in 1 hour for security reasons.
              </p>
            </div>
          </div>
        `
      },
      ar: {
        subject: 'إعادة تعيين كلمة المرور - ويسترون لتحويل الأموال',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; direction: rtl;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
              <h1 style="color: white; margin: 0;">ويسترون لتحويل الأموال</h1>
            </div>
            <div style="padding: 30px; background: #f9f9f9;">
              <h2 style="color: #333;">إعادة تعيين كلمة المرور</h2>
              <p style="color: #666; line-height: 1.6;">
                لقد طلبت إعادة تعيين كلمة المرور لحسابك في ويسترون لتحويل الأموال. انقر على الزر أدناه لإعادة تعيين كلمة المرور.
              </p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}" 
                   style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                  إعادة تعيين كلمة المرور
                </a>
              </div>
              <p style="color: #999; font-size: 14px;">
                إذا لم تطلب إعادة التعيين، يرجى تجاهل هذا البريد الإلكتروني.
              </p>
              <p style="color: #999; font-size: 14px;">
                ستنتهي صلاحية هذا الرابط خلال ساعة واحدة لأسباب أمنية.
              </p>
            </div>
          </div>
        `
      }
    };

    const template = templates[language] || templates.en;

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: email,
      subject: template.subject,
      html: template.html
    };

    const result = await transporter.sendMail(mailOptions);
    
    logger.info('Password reset email sent successfully', {
      email,
      messageId: result.messageId,
      language
    });

    return result;
  } catch (error) {
    logger.error('Failed to send password reset email:', {
      email,
      error: error.message,
      language
    });
    throw error;
  }
};

/**
 * Send transfer notification email
 */
const sendTransferNotification = async (email, transferData, language = 'en') => {
  try {
    const transporter = createTransporter();
    
    const templates = {
      en: {
        subject: `Transfer Confirmation - ${transferData.referenceNumber}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
              <h1 style="color: white; margin: 0;">Wistron Money Transfer</h1>
            </div>
            <div style="padding: 30px; background: #f9f9f9;">
              <h2 style="color: #333;">Transfer Confirmation</h2>
              <p style="color: #666;">Your money transfer has been processed successfully.</p>
              
              <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #333; margin-top: 0;">Transfer Details</h3>
                <p><strong>Reference Number:</strong> ${transferData.referenceNumber}</p>
                <p><strong>Tracking Number:</strong> ${transferData.trackingNumber}</p>
                <p><strong>Amount Sent:</strong> ${transferData.sendAmount} ${transferData.sendCurrency}</p>
                <p><strong>Amount to Receive:</strong> ${transferData.receiveAmount} ${transferData.receiveCurrency}</p>
                <p><strong>Recipient:</strong> ${transferData.recipientName}</p>
                <p><strong>Status:</strong> ${transferData.status}</p>
              </div>
              
              <p style="color: #666; font-size: 14px;">
                You can track your transfer status anytime using the tracking number above.
              </p>
            </div>
          </div>
        `
      }
    };

    const template = templates[language] || templates.en;

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: email,
      subject: template.subject,
      html: template.html
    };

    const result = await transporter.sendMail(mailOptions);
    
    logger.info('Transfer notification email sent successfully', {
      email,
      transferId: transferData.id,
      messageId: result.messageId,
      language
    });

    return result;
  } catch (error) {
    logger.error('Failed to send transfer notification email:', {
      email,
      transferId: transferData.id,
      error: error.message,
      language
    });
    throw error;
  }
};

module.exports = {
  sendVerificationEmail,
  sendPasswordResetEmail,
  sendTransferNotification
};
