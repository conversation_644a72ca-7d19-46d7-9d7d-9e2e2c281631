// Test login for final system
const http = require('http');

function testLogin() {
  const loginData = JSON.stringify({
    email: '<EMAIL>',
    password: 'Admin@123456'
  });

  const req = http.request({
    hostname: 'localhost',
    port: 5000,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(loginData)
    }
  }, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      console.log('🔐 Testing Admin Login...');
      console.log(`Status: ${res.statusCode}`);
      
      try {
        const response = JSON.parse(data);
        if (response.success) {
          console.log('✅ Login successful!');
          console.log(`User: ${response.data.user.email}`);
          console.log(`Role: ${response.data.user.role}`);
          console.log(`Token: ${response.data.tokens.accessToken.substring(0, 30)}...`);
        } else {
          console.log('❌ Login failed:', response.error);
        }
      } catch (e) {
        console.log('Response:', data);
      }
      
      console.log('\n🎉 Final System Login Test Complete!');
    });
  });

  req.on('error', (err) => {
    console.log('❌ Request error:', err.message);
  });

  req.write(loginData);
  req.end();
}

testLogin();
