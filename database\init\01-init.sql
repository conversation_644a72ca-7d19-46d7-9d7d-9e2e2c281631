-- Wistron Money Transfer Database Initialization
-- إعداد قاعدة بيانات ويسترون لتحويل الأموال

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS wistron_money_transfer;

-- Connect to the database
\c wistron_money_transfer;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('customer', 'agent', 'admin', 'super_admin');
CREATE TYPE user_status AS ENUM ('pending', 'active', 'suspended', 'closed');
CREATE TYPE kyc_status AS ENUM ('not_started', 'pending', 'approved', 'rejected');
CREATE TYPE transfer_status AS ENUM ('pending', 'processing', 'ready_for_pickup', 'completed', 'cancelled', 'refunded', 'failed');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'failed', 'refunded');
CREATE TYPE delivery_method AS ENUM ('cash_pickup', 'bank_deposit', 'mobile_wallet', 'home_delivery');
CREATE TYPE gender AS ENUM ('male', 'female', 'other');

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create function to generate reference numbers
CREATE OR REPLACE FUNCTION generate_reference_number()
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ**********';
    result TEXT := 'WMT';
    i INTEGER;
BEGIN
    FOR i IN 1..8 LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create function to generate tracking numbers
CREATE OR REPLACE FUNCTION generate_tracking_number()
RETURNS TEXT AS $$
DECLARE
    chars TEXT := '**********';
    result TEXT := '';
    i INTEGER;
BEGIN
    FOR i IN 1..12 LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create audit log function
CREATE OR REPLACE FUNCTION create_audit_log()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (table_name, operation, record_id, new_data, created_at)
        VALUES (TG_TABLE_NAME, TG_OP, NEW.id, row_to_json(NEW), CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (table_name, operation, record_id, old_data, new_data, created_at)
        VALUES (TG_TABLE_NAME, TG_OP, NEW.id, row_to_json(OLD), row_to_json(NEW), CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (table_name, operation, record_id, old_data, created_at)
        VALUES (TG_TABLE_NAME, TG_OP, OLD.id, row_to_json(OLD), CURRENT_TIMESTAMP);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users USING btree (email);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_phone ON users USING btree (phone);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status ON users USING btree (status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role ON users USING btree (role);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_kyc_status ON users USING btree (kyc_status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users USING btree (created_at);

-- Create full-text search indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_search ON users USING gin (
    to_tsvector('english', coalesce(first_name, '') || ' ' || coalesce(last_name, '') || ' ' || coalesce(email, ''))
);

-- Create composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status_role ON users USING btree (status, role);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_kyc_created ON users USING btree (kyc_status, created_at);

-- Grant permissions to application user
GRANT USAGE ON SCHEMA public TO wistron_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO wistron_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO wistron_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO wistron_user;

-- Create default admin user (password: Admin@123456)
INSERT INTO users (
    id,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    status,
    kyc_status,
    is_email_verified,
    preferred_language,
    created_at,
    updated_at
) VALUES (
    uuid_generate_v4(),
    '<EMAIL>',
    '$2b$12$LQv3c1yqBw2uuCD4mi8.Oe5a6fv9UVgn6oI4uIdsC8ocmxlUQs.KS', -- Admin@123456
    'System',
    'Administrator',
    'super_admin',
    'active',
    'approved',
    true,
    'en',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO NOTHING;

-- Create sample agent user (password: Agent@123456)
INSERT INTO users (
    id,
    email,
    password_hash,
    first_name,
    last_name,
    role,
    status,
    kyc_status,
    is_email_verified,
    preferred_language,
    created_at,
    updated_at
) VALUES (
    uuid_generate_v4(),
    '<EMAIL>',
    '$2b$12$8Ry3VQZ9X1YQZ9X1YQZ9X1YQZ9X1YQZ9X1YQZ9X1YQZ9X1YQZ9X1Y', -- Agent@123456
    'Sample',
    'Agent',
    'agent',
    'active',
    'approved',
    true,
    'en',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (email) DO NOTHING;

-- Create performance monitoring views
CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    role,
    status,
    kyc_status,
    COUNT(*) as count,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as last_30_days,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days') as last_7_days
FROM users 
GROUP BY role, status, kyc_status;

CREATE OR REPLACE VIEW transfer_statistics AS
SELECT 
    status,
    delivery_method,
    COUNT(*) as count,
    SUM(send_amount) as total_amount,
    AVG(send_amount) as avg_amount,
    COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as last_30_days,
    SUM(send_amount) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as amount_last_30_days
FROM transfers 
GROUP BY status, delivery_method;

-- Create materialized view for dashboard metrics (refresh daily)
CREATE MATERIALIZED VIEW IF NOT EXISTS dashboard_metrics AS
SELECT 
    (SELECT COUNT(*) FROM users WHERE status = 'active') as active_users,
    (SELECT COUNT(*) FROM transfers WHERE status = 'completed') as completed_transfers,
    (SELECT SUM(send_amount) FROM transfers WHERE status = 'completed') as total_volume,
    (SELECT COUNT(*) FROM transfers WHERE created_at >= CURRENT_DATE) as today_transfers,
    (SELECT SUM(send_amount) FROM transfers WHERE created_at >= CURRENT_DATE) as today_volume,
    CURRENT_TIMESTAMP as last_updated;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_dashboard_metrics_updated ON dashboard_metrics (last_updated);

-- Set up automatic refresh of materialized view (requires pg_cron extension in production)
-- SELECT cron.schedule('refresh-dashboard-metrics', '0 1 * * *', 'REFRESH MATERIALIZED VIEW dashboard_metrics;');

COMMIT;
