// Full API Test for Wistron Money Transfer
const http = require('http');

console.log('🧪 Running Full API Test for Wistron Money Transfer...\n');

const tests = [
  {
    name: 'Health Check',
    method: 'GET',
    path: '/health',
    expected: 200
  },
  {
    name: 'Exchange Rates',
    method: 'GET', 
    path: '/api/v1/rates',
    expected: 200
  },
  {
    name: 'Valid Login',
    method: 'POST',
    path: '/api/v1/auth/login',
    data: { email: '<EMAIL>', password: 'Admin@123456' },
    expected: 200
  },
  {
    name: 'Invalid Login',
    method: 'POST',
    path: '/api/v1/auth/login', 
    data: { email: '<EMAIL>', password: 'wrongpass' },
    expected: 401
  },
  {
    name: 'Non-existent Route',
    method: 'GET',
    path: '/api/v1/nonexistent',
    expected: 404
  }
];

let currentTest = 0;

function runNextTest() {
  if (currentTest >= tests.length) {
    showFinalResults();
    return;
  }
  
  const test = tests[currentTest];
  console.log(`${currentTest + 1}. Testing: ${test.name}`);
  
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: test.path,
    method: test.method,
    headers: test.data ? {
      'Content-Type': 'application/json'
    } : {}
  };
  
  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      const success = res.statusCode === test.expected;
      console.log(`   ${success ? '✅' : '❌'} Status: ${res.statusCode} (expected: ${test.expected})`);
      
      try {
        const response = JSON.parse(data);
        if (response.success !== undefined) {
          console.log(`   Response: ${response.success ? 'Success' : 'Failed'} - ${response.message || response.error}`);
        }
      } catch (e) {
        // Not JSON response
      }
      
      console.log('');
      currentTest++;
      setTimeout(runNextTest, 100);
    });
  });
  
  req.on('error', (err) => {
    console.log(`   ❌ Request failed: ${err.message}\n`);
    currentTest++;
    setTimeout(runNextTest, 100);
  });
  
  if (test.data) {
    req.write(JSON.stringify(test.data));
  }
  
  req.end();
}

function showFinalResults() {
  console.log('🎉 ========================================');
  console.log('   Wistron Money Transfer - Test Results');
  console.log('   نظام ويسترون - نتائج الاختبار');
  console.log('========================================');
  console.log('✅ جميع الاختبارات اكتملت!');
  console.log('');
  console.log('🌐 النظام متاح على:');
  console.log('   الصفحة الرئيسية: http://localhost:5000');
  console.log('   فحص الصحة: http://localhost:5000/health');
  console.log('   أسعار الصرف: http://localhost:5000/api/v1/rates');
  console.log('');
  console.log('👤 حسابات التجربة:');
  console.log('   المدير: <EMAIL> / Admin@123456');
  console.log('   الوكيل: <EMAIL> / Agent@123456');
  console.log('');
  console.log('📝 ملاحظة: إذا واجهت خطأ "Route not found"،');
  console.log('   تأكد من أن المسار صحيح والخادم يعمل');
  console.log('========================================');
}

// Start testing
runNextTest();
