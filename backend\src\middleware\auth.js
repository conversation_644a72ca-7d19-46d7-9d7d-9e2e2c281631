/**
 * Authentication Middleware
 * وسطاء التوثيق والأمان
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const speakeasy = require('speakeasy');
const { redisUtils } = require('../config/redis');
const db = require('../config/database');
const logger = require('../utils/logger');
const { AppError } = require('../utils/errors');

/**
 * Generate JWT tokens
 * @param {Object} payload - Token payload
 * @returns {Object} Access and refresh tokens
 */
const generateTokens = (payload) => {
  const accessToken = jwt.sign(
    payload,
    process.env.JWT_SECRET,
    { 
      expiresIn: process.env.JWT_EXPIRE || '24h',
      issuer: 'wistron-money-transfer',
      audience: 'wistron-users'
    }
  );

  const refreshToken = jwt.sign(
    { userId: payload.userId, tokenType: 'refresh' },
    process.env.JWT_REFRESH_SECRET,
    { 
      expiresIn: process.env.JWT_REFRESH_EXPIRE || '7d',
      issuer: 'wistron-money-transfer',
      audience: 'wistron-users'
    }
  );

  return { accessToken, refreshToken };
};

/**
 * Verify JWT token
 * @param {string} token - JWT token
 * @param {string} secret - JWT secret
 * @returns {Object} Decoded token
 */
const verifyToken = (token, secret) => {
  try {
    return jwt.verify(token, secret, {
      issuer: 'wistron-money-transfer',
      audience: 'wistron-users'
    });
  } catch (error) {
    throw new AppError('Invalid or expired token', 401);
  }
};

/**
 * Hash password
 * @param {string} password - Plain text password
 * @returns {string} Hashed password
 */
const hashPassword = async (password) => {
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
  return await bcrypt.hash(password, saltRounds);
};

/**
 * Compare password
 * @param {string} password - Plain text password
 * @param {string} hashedPassword - Hashed password
 * @returns {boolean} Password match result
 */
const comparePassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword);
};

/**
 * Generate 2FA secret
 * @param {string} userEmail - User email
 * @returns {Object} 2FA secret and QR code
 */
const generate2FASecret = (userEmail) => {
  const secret = speakeasy.generateSecret({
    name: `Wistron Money Transfer (${userEmail})`,
    issuer: 'Wistron Money Transfer',
    length: 32
  });

  return {
    secret: secret.base32,
    qrCode: secret.otpauth_url
  };
};

/**
 * Verify 2FA token
 * @param {string} token - 2FA token
 * @param {string} secret - 2FA secret
 * @returns {boolean} Verification result
 */
const verify2FAToken = (token, secret) => {
  return speakeasy.totp.verify({
    secret: secret,
    encoding: 'base32',
    token: token,
    window: 2 // Allow 2 time steps (60 seconds) tolerance
  });
};

/**
 * Authentication middleware
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new AppError('Access token required', 401);
    }

    // Check if token is blacklisted
    const isBlacklisted = await redisUtils.exists(`blacklist:${token}`);
    if (isBlacklisted) {
      throw new AppError('Token has been revoked', 401);
    }

    // Verify token
    const decoded = verifyToken(token, process.env.JWT_SECRET);

    // Get user from database
    const user = await db('users')
      .select('id', 'email', 'role', 'status', 'is_2fa_enabled', 'last_login')
      .where('id', decoded.userId)
      .first();

    if (!user) {
      throw new AppError('User not found', 401);
    }

    if (user.status !== 'active') {
      throw new AppError('Account is not active', 401);
    }

    // Add user info to request
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
      is2FAEnabled: user.is_2fa_enabled,
      lastLogin: user.last_login
    };

    // Store token in Redis for session management
    await redisUtils.set(
      `session:${user.id}:${token}`,
      { userId: user.id, loginTime: new Date() },
      24 * 60 * 60 // 24 hours
    );

    next();
  } catch (error) {
    logger.error('Authentication error:', error.message);
    return res.status(error.statusCode || 401).json({
      error: 'Authentication failed',
      message: error.message
    });
  }
};

/**
 * Role-based authorization middleware
 * @param {Array} allowedRoles - Array of allowed roles
 */
const authorize = (allowedRoles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        throw new AppError('Authentication required', 401);
      }

      if (!allowedRoles.includes(req.user.role)) {
        throw new AppError('Insufficient permissions', 403);
      }

      next();
    } catch (error) {
      logger.error('Authorization error:', error.message);
      return res.status(error.statusCode || 403).json({
        error: 'Authorization failed',
        message: error.message
      });
    }
  };
};

/**
 * 2FA verification middleware
 */
const require2FA = async (req, res, next) => {
  try {
    if (!req.user) {
      throw new AppError('Authentication required', 401);
    }

    if (!req.user.is2FAEnabled) {
      return next(); // Skip 2FA if not enabled
    }

    const twoFAToken = req.headers['x-2fa-token'];
    if (!twoFAToken) {
      throw new AppError('2FA token required', 401);
    }

    // Get user's 2FA secret
    const userSecret = await db('user_2fa')
      .select('secret')
      .where('user_id', req.user.id)
      .where('is_active', true)
      .first();

    if (!userSecret) {
      throw new AppError('2FA not properly configured', 401);
    }

    // Verify 2FA token
    const isValid = verify2FAToken(twoFAToken, userSecret.secret);
    if (!isValid) {
      throw new AppError('Invalid 2FA token', 401);
    }

    next();
  } catch (error) {
    logger.error('2FA verification error:', error.message);
    return res.status(error.statusCode || 401).json({
      error: '2FA verification failed',
      message: error.message
    });
  }
};

/**
 * Refresh token middleware
 */
const refreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw new AppError('Refresh token required', 401);
    }

    // Verify refresh token
    const decoded = verifyToken(refreshToken, process.env.JWT_REFRESH_SECRET);

    if (decoded.tokenType !== 'refresh') {
      throw new AppError('Invalid token type', 401);
    }

    // Check if refresh token exists in database
    const storedToken = await db('refresh_tokens')
      .where('user_id', decoded.userId)
      .where('token', refreshToken)
      .where('expires_at', '>', new Date())
      .where('is_revoked', false)
      .first();

    if (!storedToken) {
      throw new AppError('Invalid or expired refresh token', 401);
    }

    // Get user
    const user = await db('users')
      .select('id', 'email', 'role', 'status')
      .where('id', decoded.userId)
      .first();

    if (!user || user.status !== 'active') {
      throw new AppError('User not found or inactive', 401);
    }

    req.user = user;
    next();
  } catch (error) {
    logger.error('Refresh token error:', error.message);
    return res.status(error.statusCode || 401).json({
      error: 'Token refresh failed',
      message: error.message
    });
  }
};

/**
 * Logout and blacklist token
 * @param {string} token - JWT token to blacklist
 */
const blacklistToken = async (token) => {
  try {
    const decoded = jwt.decode(token);
    if (decoded && decoded.exp) {
      const ttl = decoded.exp - Math.floor(Date.now() / 1000);
      if (ttl > 0) {
        await redisUtils.set(`blacklist:${token}`, true, ttl);
      }
    }
  } catch (error) {
    logger.error('Token blacklist error:', error.message);
  }
};

module.exports = {
  generateTokens,
  verifyToken,
  hashPassword,
  comparePassword,
  generate2FASecret,
  verify2FAToken,
  authenticateToken,
  authorize,
  require2FA,
  refreshToken,
  blacklistToken
};
