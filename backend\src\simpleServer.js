/**
 * Simple Development Server
 * خادم تطوير مبسط بدون قاعدة بيانات
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

// Simple logger
const logger = require('./utils/simpleLogger');

const app = express();

// Trust proxy for rate limiting
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  }
}));

// CORS configuration
app.use(cors({
  origin: "http://localhost:3000",
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: 15 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.http(message.trim())
  }
}));

// In-memory data store for demo
const users = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'Admin@123456', // In real app, this would be hashed
    firstName: 'System',
    lastName: 'Administrator',
    role: 'super_admin',
    status: 'active',
    kycStatus: 'approved',
    is2FAEnabled: false,
    isEmailVerified: true,
    isPhoneVerified: false,
    createdAt: new Date().toISOString()
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'Agent@123456',
    firstName: 'Sample',
    lastName: 'Agent',
    role: 'agent',
    status: 'active',
    kycStatus: 'approved',
    is2FAEnabled: false,
    isEmailVerified: true,
    isPhoneVerified: false,
    createdAt: new Date().toISOString()
  }
];

const transfers = [];

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: 'development-simple',
    version: '1.0.0',
    message: 'Wistron Money Transfer - Simple Development Server'
  });
});

// Simple auth endpoints
app.post('/api/v1/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  logger.info('Login attempt', { email });
  
  const user = users.find(u => u.email === email && u.password === password);
  
  if (!user) {
    logger.warn('Failed login attempt', { email });
    return res.status(401).json({
      success: false,
      error: 'Invalid credentials'
    });
  }
  
  // Simple token (in real app, use JWT)
  const token = Buffer.from(`${user.id}:${Date.now()}`).toString('base64');
  
  logger.audit('USER_LOGIN', user.id, { email });
  
  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        kycStatus: user.kycStatus,
        is2FAEnabled: user.is2FAEnabled,
        isEmailVerified: user.isEmailVerified,
        isPhoneVerified: user.isPhoneVerified
      },
      tokens: {
        accessToken: token,
        refreshToken: token
      }
    }
  });
});

app.post('/api/v1/auth/register', (req, res) => {
  const { email, password, firstName, lastName, country } = req.body;
  
  logger.info('Registration attempt', { email });
  
  // Check if user exists
  if (users.find(u => u.email === email)) {
    return res.status(409).json({
      success: false,
      error: 'Email already registered'
    });
  }
  
  // Create new user
  const newUser = {
    id: String(users.length + 1),
    email,
    password, // In real app, hash this
    firstName,
    lastName,
    country,
    role: 'customer',
    status: 'pending',
    kycStatus: 'not_started',
    is2FAEnabled: false,
    isEmailVerified: false,
    isPhoneVerified: false,
    createdAt: new Date().toISOString()
  };
  
  users.push(newUser);
  
  logger.audit('USER_REGISTERED', newUser.id, { email });
  
  res.status(201).json({
    success: true,
    message: 'Registration successful. Please check your email for verification.',
    data: {
      user: {
        id: newUser.id,
        email: newUser.email,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        status: newUser.status,
        createdAt: newUser.createdAt
      }
    }
  });
});

// Simple dashboard stats
app.get('/api/v1/dashboard/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalUsers: users.length,
      activeUsers: users.filter(u => u.status === 'active').length,
      totalTransfers: transfers.length,
      completedTransfers: transfers.filter(t => t.status === 'completed').length,
      totalVolume: transfers.reduce((sum, t) => sum + (t.amount || 0), 0),
      todayTransfers: 0,
      todayVolume: 0
    }
  });
});

// Exchange rates endpoint
app.get('/api/v1/rates', (req, res) => {
  res.json({
    success: true,
    data: {
      base: 'USD',
      rates: {
        SAR: 3.75,
        AED: 3.67,
        EGP: 30.85,
        JOD: 0.71,
        KWD: 0.31,
        QAR: 3.64,
        BHD: 0.38,
        OMR: 0.38,
        EUR: 0.85,
        GBP: 0.73
      },
      lastUpdated: new Date().toISOString()
    }
  });
});

// API Documentation (simple)
app.get('/api-docs', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>Wistron API Documentation</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          h1 { color: #333; }
          .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
          .method { font-weight: bold; color: #007bff; }
        </style>
      </head>
      <body>
        <h1>🏦 Wistron Money Transfer API</h1>
        <p>Simple Development API Documentation</p>
        
        <div class="endpoint">
          <div class="method">GET /health</div>
          <p>Health check endpoint</p>
        </div>
        
        <div class="endpoint">
          <div class="method">POST /api/v1/auth/login</div>
          <p>User login</p>
          <pre>{ "email": "<EMAIL>", "password": "Admin@123456" }</pre>
        </div>
        
        <div class="endpoint">
          <div class="method">POST /api/v1/auth/register</div>
          <p>User registration</p>
          <pre>{ "email": "<EMAIL>", "password": "Password@123", "firstName": "John", "lastName": "Doe", "country": "US" }</pre>
        </div>
        
        <div class="endpoint">
          <div class="method">GET /api/v1/dashboard/stats</div>
          <p>Dashboard statistics</p>
        </div>
        
        <div class="endpoint">
          <div class="method">GET /api/v1/rates</div>
          <p>Exchange rates</p>
        </div>
        
        <h3>Test Accounts:</h3>
        <ul>
          <li><strong>Admin:</strong> <EMAIL> / Admin@123456</li>
          <li><strong>Agent:</strong> <EMAIL> / Agent@123456</li>
        </ul>
      </body>
    </html>
  `);
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    message: `The requested route ${req.originalUrl} does not exist.`
  });
});

// Error handler
app.use((error, req, res, next) => {
  logger.error('Server error:', { error: error.message, stack: error.stack });
  
  res.status(error.statusCode || 500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  logger.info(`🚀 Wistron Money Transfer Server running on port ${PORT}`);
  logger.info(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
  logger.info(`🏥 Health Check: http://localhost:${PORT}/health`);
  logger.info(`🌍 Environment: development-simple`);
  logger.info(`⚠️  Note: Using in-memory storage - data will be lost on restart`);
});

module.exports = app;
