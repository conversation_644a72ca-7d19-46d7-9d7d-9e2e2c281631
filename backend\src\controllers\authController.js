/**
 * Authentication Controller
 * تحكم في عمليات التوثيق والأمان
 */

const { validationResult } = require('express-validator');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const db = require('../config/database');
const { redisUtils } = require('../config/redis');
const logger = require('../utils/logger');
const { 
  AppError, 
  ValidationError, 
  AuthenticationError,
  ERROR_CODES 
} = require('../utils/errors');
const {
  generateTokens,
  hashPassword,
  comparePassword,
  generate2FASecret,
  verify2FAToken,
  blacklistToken
} = require('../middleware/auth');
const emailService = require('../services/emailService');
const smsService = require('../services/smsService');

/**
 * Register a new user
 */
const register = async (req, res) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const {
    email,
    password,
    firstName,
    lastName,
    phone,
    dateOfBirth,
    country,
    preferredLanguage = 'en'
  } = req.body;

  // Check if user already exists
  const existingUser = await db('users')
    .where('email', email)
    .orWhere('phone', phone)
    .first();

  if (existingUser) {
    if (existingUser.email === email) {
      throw new AppError('Email already registered', 409, ERROR_CODES.DUPLICATE_TRANSACTION);
    }
    if (existingUser.phone === phone) {
      throw new AppError('Phone number already registered', 409, ERROR_CODES.DUPLICATE_TRANSACTION);
    }
  }

  // Hash password
  const passwordHash = await hashPassword(password);

  // Generate verification tokens
  const emailVerificationToken = crypto.randomBytes(32).toString('hex');
  const phoneVerificationToken = Math.floor(100000 + Math.random() * 900000).toString();

  // Create user
  const [user] = await db('users')
    .insert({
      email,
      password_hash: passwordHash,
      first_name: firstName,
      last_name: lastName,
      phone,
      date_of_birth: dateOfBirth,
      country,
      preferred_language: preferredLanguage,
      email_verification_token: emailVerificationToken,
      phone_verification_token: phoneVerificationToken,
      status: 'pending'
    })
    .returning(['id', 'email', 'first_name', 'last_name', 'status', 'created_at']);

  // Send verification emails/SMS
  try {
    await emailService.sendVerificationEmail(email, emailVerificationToken, preferredLanguage);
    
    if (phone) {
      await smsService.sendVerificationSMS(phone, phoneVerificationToken, preferredLanguage);
    }
  } catch (error) {
    logger.error('Failed to send verification messages:', error);
    // Don't fail registration if email/SMS fails
  }

  // Log registration
  logger.audit('USER_REGISTERED', user.id, {
    email: user.email,
    registrationMethod: 'email',
    country
  });

  res.status(201).json({
    success: true,
    message: 'Registration successful. Please check your email and phone for verification.',
    data: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        status: user.status,
        createdAt: user.created_at
      }
    }
  });
};

/**
 * Login user
 */
const login = async (req, res) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { email, password, twoFAToken } = req.body;
  const clientIP = req.ip;
  const userAgent = req.get('User-Agent');

  // Get user
  const user = await db('users')
    .where('email', email)
    .first();

  if (!user) {
    throw new AuthenticationError('Invalid credentials');
  }

  // Check if account is locked
  if (user.locked_until && new Date(user.locked_until) > new Date()) {
    const lockTimeRemaining = Math.ceil((new Date(user.locked_until) - new Date()) / 1000 / 60);
    throw new AppError(
      `Account is locked. Try again in ${lockTimeRemaining} minutes.`,
      423,
      ERROR_CODES.ACCOUNT_LOCKED
    );
  }

  // Check password
  const isPasswordValid = await comparePassword(password, user.password_hash);
  
  if (!isPasswordValid) {
    // Increment failed login attempts
    const failedAttempts = user.failed_login_attempts + 1;
    const updateData = { failed_login_attempts: failedAttempts };
    
    // Lock account after 5 failed attempts
    if (failedAttempts >= 5) {
      updateData.locked_until = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
    }
    
    await db('users').where('id', user.id).update(updateData);
    
    logger.security('FAILED_LOGIN_ATTEMPT', {
      userId: user.id,
      email: user.email,
      ip: clientIP,
      userAgent,
      failedAttempts
    });
    
    throw new AuthenticationError('Invalid credentials');
  }

  // Check account status
  if (user.status !== 'active') {
    throw new AppError('Account is not active', 401, ERROR_CODES.ACCOUNT_SUSPENDED);
  }

  // Check 2FA if enabled
  if (user.is_2fa_enabled) {
    if (!twoFAToken) {
      return res.status(200).json({
        success: true,
        requiresTwoFA: true,
        message: '2FA token required'
      });
    }

    // Get user's 2FA secret
    const userSecret = await db('user_2fa')
      .select('secret')
      .where('user_id', user.id)
      .where('is_active', true)
      .first();

    if (!userSecret || !verify2FAToken(twoFAToken, userSecret.secret)) {
      throw new AppError('Invalid 2FA token', 401, ERROR_CODES.TWO_FA_INVALID);
    }
  }

  // Generate tokens
  const tokens = generateTokens({
    userId: user.id,
    email: user.email,
    role: user.role
  });

  // Store refresh token in database
  await db('refresh_tokens').insert({
    user_id: user.id,
    token: tokens.refreshToken,
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    created_ip: clientIP,
    user_agent: userAgent
  });

  // Update user login info
  await db('users')
    .where('id', user.id)
    .update({
      last_login: new Date(),
      last_login_ip: clientIP,
      failed_login_attempts: 0,
      locked_until: null
    });

  // Log successful login
  logger.audit('USER_LOGIN', user.id, {
    email: user.email,
    ip: clientIP,
    userAgent,
    loginMethod: user.is_2fa_enabled ? '2FA' : 'password'
  });

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        status: user.status,
        kycStatus: user.kyc_status,
        is2FAEnabled: user.is_2fa_enabled,
        isEmailVerified: user.is_email_verified,
        isPhoneVerified: user.is_phone_verified
      },
      tokens: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken
      }
    }
  });
};

/**
 * Logout user
 */
const logout = async (req, res) => {
  const token = req.headers.authorization?.split(' ')[1];
  const userId = req.user.id;

  // Blacklist current token
  if (token) {
    await blacklistToken(token);
  }

  // Revoke all refresh tokens for this user
  await db('refresh_tokens')
    .where('user_id', userId)
    .update({ is_revoked: true });

  // Clear user sessions from Redis
  const sessionKeys = await redisUtils.smembers(`user_sessions:${userId}`);
  for (const sessionKey of sessionKeys) {
    await redisUtils.del(sessionKey);
  }
  await redisUtils.del(`user_sessions:${userId}`);

  logger.audit('USER_LOGOUT', userId, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: 'Logout successful'
  });
};

/**
 * Refresh access token
 */
const refreshToken = async (req, res) => {
  const user = req.user;

  // Generate new tokens
  const tokens = generateTokens({
    userId: user.id,
    email: user.email,
    role: user.role
  });

  // Store new refresh token
  await db('refresh_tokens').insert({
    user_id: user.id,
    token: tokens.refreshToken,
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    created_ip: req.ip,
    user_agent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: 'Token refreshed successfully',
    data: {
      tokens: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken
      }
    }
  });
};

/**
 * Get current user profile
 */
const getCurrentUser = async (req, res) => {
  const userId = req.user.id;

  const user = await db('users')
    .select([
      'id', 'email', 'first_name', 'last_name', 'phone', 'date_of_birth',
      'role', 'status', 'kyc_status', 'is_2fa_enabled', 'is_email_verified',
      'is_phone_verified', 'preferred_language', 'preferred_currency',
      'created_at', 'last_login'
    ])
    .where('id', userId)
    .first();

  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.json({
    success: true,
    data: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phone: user.phone,
        dateOfBirth: user.date_of_birth,
        role: user.role,
        status: user.status,
        kycStatus: user.kyc_status,
        is2FAEnabled: user.is_2fa_enabled,
        isEmailVerified: user.is_email_verified,
        isPhoneVerified: user.is_phone_verified,
        preferredLanguage: user.preferred_language,
        preferredCurrency: user.preferred_currency,
        createdAt: user.created_at,
        lastLogin: user.last_login
      }
    }
  });
};

/**
 * Setup 2FA for user
 */
const setup2FA = async (req, res) => {
  const userId = req.user.id;

  // Generate 2FA secret
  const { secret, qrCode } = generate2FASecret(req.user.email);

  // Store secret temporarily (not activated yet)
  await db('user_2fa')
    .insert({
      user_id: userId,
      secret: secret,
      is_active: false
    })
    .onConflict('user_id')
    .merge(['secret', 'is_active', 'updated_at']);

  res.json({
    success: true,
    message: '2FA setup initiated. Please scan the QR code and verify with your authenticator app.',
    data: {
      secret,
      qrCode
    }
  });
};

/**
 * Verify 2FA token and activate
 */
const verify2FA = async (req, res) => {
  const { token } = req.body;
  const userId = req.user.id;

  // Get user's 2FA secret
  const userSecret = await db('user_2fa')
    .select('secret')
    .where('user_id', userId)
    .first();

  if (!userSecret) {
    throw new AppError('2FA not set up', 400);
  }

  // Verify token
  const isValid = verify2FAToken(token, userSecret.secret);
  if (!isValid) {
    throw new AppError('Invalid 2FA token', 400, ERROR_CODES.TWO_FA_INVALID);
  }

  // Activate 2FA
  await db.transaction(async (trx) => {
    await trx('user_2fa')
      .where('user_id', userId)
      .update({ is_active: true });

    await trx('users')
      .where('id', userId)
      .update({ is_2fa_enabled: true });
  });

  logger.audit('2FA_ENABLED', userId, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: '2FA has been successfully enabled for your account'
  });
};

module.exports = {
  register,
  login,
  logout,
  refreshToken,
  getCurrentUser,
  setup2FA,
  verify2FA
};
