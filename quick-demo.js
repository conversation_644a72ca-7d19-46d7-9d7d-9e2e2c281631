// Wistron Money Transfer - Quick Demo Server
const http = require('http');
const url = require('url');

const users = [
  { id: '1', email: '<EMAIL>', password: 'Admin@123456', role: 'admin' },
  { id: '2', email: '<EMAIL>', password: 'Agent@123456', role: 'agent' }
];

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Home page
  if (path === '/' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>🏦 Wistron Money Transfer</title>
        <style>
          body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 40px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
          }
          .container { max-width: 800px; margin: 0 auto; text-align: center; }
          h1 { font-size: 3em; margin-bottom: 20px; }
          .card { 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            margin: 20px 0;
            backdrop-filter: blur(10px);
          }
          .btn { 
            display: inline-block;
            padding: 15px 30px; 
            background: white; 
            color: #667eea; 
            text-decoration: none; 
            border-radius: 8px; 
            margin: 10px;
            font-weight: bold;
          }
          .status { color: #4ade80; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🏦 Wistron Money Transfer</h1>
          <p>نظام ويسترون المتكامل لتحويل الأموال</p>
          
          <div class="card">
            <h2>🚀 النظام يعمل بنجاح!</h2>
            <p class="status">✅ Server Status: RUNNING</p>
            <p>الخادم التجريبي يعمل على المنفذ 5000</p>
          </div>
          
          <div class="card">
            <h3>🔗 الروابط المهمة</h3>
            <a href="/api-docs" class="btn">📚 توثيق API</a>
            <a href="/health" class="btn">🏥 فحص الصحة</a>
            <a href="/api/v1/rates" class="btn">💱 أسعار الصرف</a>
          </div>
          
          <div class="card">
            <h3>👤 حسابات التجربة</h3>
            <p><strong>المدير:</strong> <EMAIL> / Admin@123456</p>
            <p><strong>الوكيل:</strong> <EMAIL> / Agent@123456</p>
          </div>
          
          <div class="card">
            <h3>⚠️ ملاحظة مهمة</h3>
            <p>هذا خادم تجريبي مبسط</p>
            <p>للحصول على النظام الكامل، يرجى تثبيت Docker</p>
          </div>
        </div>
      </body>
      </html>
    `);
    return;
  }

  // Health check
  if (path === '/health' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'OK',
      message: 'Wistron Money Transfer Demo Server',
      timestamp: new Date().toISOString(),
      version: '1.0.0-demo'
    }));
    return;
  }

  // Login endpoint
  if (path === '/api/v1/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      try {
        const { email, password } = JSON.parse(body);
        const user = users.find(u => u.email === email && u.password === password);
        
        if (!user) {
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ success: false, error: 'Invalid credentials' }));
          return;
        }
        
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: true,
          message: 'Login successful',
          data: {
            user: { id: user.id, email: user.email, role: user.role },
            tokens: { accessToken: 'demo-token-' + user.id }
          }
        }));
      } catch (e) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Exchange rates
  if (path === '/api/v1/rates' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: {
        base: 'USD',
        rates: { SAR: 3.75, AED: 3.67, EGP: 30.85, EUR: 0.85, GBP: 0.73 },
        lastUpdated: new Date().toISOString()
      }
    }));
    return;
  }

  // API Documentation
  if (path === '/api-docs' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Wistron API Documentation</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
          .container { max-width: 1000px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
          h1 { color: #667eea; }
          .endpoint { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; }
          .method { background: #007bff; color: white; padding: 5px 15px; border-radius: 20px; }
          pre { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🏦 Wistron Money Transfer API</h1>
          
          <div class="endpoint">
            <span class="method">GET</span> <strong>/health</strong>
            <p>فحص صحة النظام</p>
          </div>
          
          <div class="endpoint">
            <span class="method">POST</span> <strong>/api/v1/auth/login</strong>
            <p>تسجيل دخول المستخدم</p>
            <pre>{ "email": "<EMAIL>", "password": "Admin@123456" }</pre>
          </div>
          
          <div class="endpoint">
            <span class="method">GET</span> <strong>/api/v1/rates</strong>
            <p>أسعار الصرف</p>
          </div>
          
          <h3>👤 حسابات التجربة:</h3>
          <ul>
            <li><strong>المدير:</strong> <EMAIL> / Admin@123456</li>
            <li><strong>الوكيل:</strong> <EMAIL> / Agent@123456</li>
          </ul>
        </div>
      </body>
      </html>
    `);
    return;
  }

  // 404
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ success: false, error: 'Route not found' }));
});

const PORT = 5000;
server.listen(PORT, () => {
  console.log(`
🏦 ========================================
   Wistron Money Transfer Demo Server
   نظام ويسترون لتحويل الأموال
========================================

🚀 Server running on: http://localhost:${PORT}
📚 API Documentation: http://localhost:${PORT}/api-docs
🏥 Health Check: http://localhost:${PORT}/health

👤 Test Accounts:
   Admin: <EMAIL> / Admin@123456
   Agent: <EMAIL> / Agent@123456

⚠️  Demo server - No database required!

========================================`);
});
