# 🚀 دليل التشغيل السريع - Wistron Money Transfer

## 📋 نظرة عامة
نظام ويسترون المتكامل لتحويل الأموال - نظام رقمي شامل لتحويل الأموال محلياً ودولياً بأحدث التقنيات.

## ⚡ التشغيل السريع (5 دقائق)

### الطريقة الأولى: Docker (الأسهل والأسرع)
```bash
# 1. استنساخ المشروع
git clone <repository-url>
cd Wistron

# 2. تشغيل النظام بالكامل
./start.sh docker
# أو في Windows
start.bat docker

# 3. انتظر حتى يكتمل التحميل (2-3 دقائق)
# 4. افتح المتصفح على http://localhost:3000
```

### الطريقة الثانية: التطوير المحلي
```bash
# 1. تأكد من وجود Node.js 18+ و Docker
node --version
docker --version

# 2. تشغيل النظام في وضع التطوير
./start.sh local
# أو في Windows
start.bat local
```

## 🌐 الوصول للنظام

| الخدمة | الرابط | الوصف |
|--------|--------|--------|
| **الواجهة الرئيسية** | http://localhost:3000 | تطبيق الويب الرئيسي |
| **API الخلفي** | http://localhost:5000 | خادم API |
| **توثيق API** | http://localhost:5000/api-docs | Swagger API Documentation |
| **قاعدة البيانات** | localhost:5432 | PostgreSQL Database |
| **Redis** | localhost:6379 | Redis Cache |
| **Grafana** | http://localhost:3001 | لوحة المراقبة |
| **Prometheus** | http://localhost:9090 | مراقبة الأداء |

## 👤 حسابات التجربة

### حساب المدير
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Admin@123456
- **الصلاحيات**: إدارة كاملة للنظام

### حساب الوكيل
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Agent@123456
- **الصلاحيات**: إدارة المعاملات والعملاء

### حساب Grafana
- **المستخدم**: admin
- **كلمة المرور**: wistron_grafana_admin_2024

## 🛠️ الأوامر المفيدة

### إدارة Docker
```bash
# عرض حالة الخدمات
docker-compose ps

# عرض السجلات
docker-compose logs -f

# إعادة تشغيل خدمة معينة
docker-compose restart backend

# إيقاف جميع الخدمات
docker-compose down

# إيقاف وحذف البيانات
docker-compose down -v
```

### تطوير Backend
```bash
cd backend

# تثبيت التبعيات
npm install

# تشغيل في وضع التطوير
npm run dev

# تشغيل الاختبارات
npm test

# تشغيل migrations
npm run migrate

# إضافة بيانات تجريبية
npm run seed
```

### تطوير Frontend
```bash
cd frontend

# تثبيت التبعيات
npm install

# تشغيل في وضع التطوير
npm run dev

# بناء للإنتاج
npm run build

# تشغيل الاختبارات
npm test
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. فشل في الاتصال بقاعدة البيانات
```bash
# تحقق من حالة قاعدة البيانات
docker-compose ps postgres

# إعادة تشغيل قاعدة البيانات
docker-compose restart postgres

# عرض سجلات قاعدة البيانات
docker-compose logs postgres
```

#### 2. خطأ في تثبيت التبعيات
```bash
# حذف node_modules وإعادة التثبيت
rm -rf backend/node_modules frontend/node_modules
cd backend && npm install
cd ../frontend && npm install
```

#### 3. مشكلة في المنافذ (Ports)
```bash
# تحقق من المنافذ المستخدمة
netstat -tulpn | grep :3000
netstat -tulpn | grep :5000

# قتل العمليات المستخدمة للمنافذ
sudo kill -9 $(lsof -ti:3000)
sudo kill -9 $(lsof -ti:5000)
```

#### 4. مشاكل الذاكرة في Docker
```bash
# تنظيف Docker
docker system prune -a

# زيادة ذاكرة Docker (في Docker Desktop)
# Settings > Resources > Memory > 4GB+
```

## 📁 هيكل المشروع

```
Wistron/
├── 📁 backend/              # Node.js API Server
│   ├── 📁 src/
│   │   ├── 📁 controllers/  # Route Controllers
│   │   ├── 📁 middleware/   # Custom Middleware
│   │   ├── 📁 models/       # Database Models
│   │   ├── 📁 routes/       # API Routes
│   │   ├── 📁 services/     # Business Logic
│   │   └── 📁 utils/        # Helper Functions
│   └── 📄 package.json
├── 📁 frontend/             # Next.js Web App
│   ├── 📁 src/
│   │   ├── 📁 components/   # React Components
│   │   ├── 📁 pages/        # Next.js Pages
│   │   ├── 📁 styles/       # CSS Styles
│   │   └── 📁 utils/        # Helper Functions
│   └── 📄 package.json
├── 📁 database/             # Database Scripts
│   ├── 📁 migrations/       # Database Migrations
│   ├── 📁 seeds/           # Sample Data
│   └── 📁 init/            # Initialization Scripts
├── 📁 docker/              # Docker Configuration
├── 📄 docker-compose.yml   # Docker Services
├── 📄 start.sh            # Linux/Mac Start Script
├── 📄 start.bat           # Windows Start Script
└── 📄 README.md           # Documentation
```

## 🧪 اختبار النظام

### 1. اختبار API
```bash
# اختبار صحة النظام
curl http://localhost:5000/health

# اختبار تسجيل مستخدم جديد
curl -X POST http://localhost:5000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test@123456",
    "firstName": "Test",
    "lastName": "User",
    "country": "US"
  }'
```

### 2. اختبار الواجهة الأمامية
1. افتح http://localhost:3000
2. انقر على "إنشاء حساب"
3. املأ النموذج وأرسله
4. تحقق من البريد الإلكتروني للتأكيد

### 3. اختبار لوحة الإدارة
1. سجل دخول بحساب المدير
2. اذهب إلى لوحة التحكم
3. تصفح الإحصائيات والتقارير

## 📊 مراقبة النظام

### Grafana Dashboards
- **System Overview**: نظرة عامة على النظام
- **API Performance**: أداء API
- **Database Metrics**: مقاييس قاعدة البيانات
- **User Activity**: نشاط المستخدمين

### Prometheus Metrics
- **HTTP Requests**: طلبات HTTP
- **Database Connections**: اتصالات قاعدة البيانات
- **Memory Usage**: استخدام الذاكرة
- **CPU Usage**: استخدام المعالج

## 🔒 الأمان

### إعدادات الأمان المفعلة
- ✅ تشفير كلمات المرور (bcrypt)
- ✅ JWT للتوثيق
- ✅ Rate Limiting
- ✅ CORS Protection
- ✅ Helmet Security Headers
- ✅ Input Validation
- ✅ SQL Injection Protection
- ✅ XSS Protection

### 2FA (المصادقة الثنائية)
1. سجل دخول لحسابك
2. اذهب إلى الإعدادات
3. فعل 2FA
4. امسح QR Code بتطبيق Authenticator

## 🌍 التدويل (i18n)

### اللغات المدعومة
- 🇺🇸 English (en)
- 🇸🇦 العربية (ar)

### إضافة لغة جديدة
1. أضف اللغة في `frontend/next-i18next.config.js`
2. أنشئ ملفات الترجمة في `frontend/public/locales/[lang]/`
3. أعد تشغيل التطبيق

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **الدردشة المباشرة**: متاحة في التطبيق
- 📱 **الهاتف**: +1-800-WISTRON
- 🌐 **الموقع**: https://wistron.com/support

### الإبلاغ عن مشاكل
1. تحقق من قائمة المشاكل المعروفة
2. ابحث في Issues على GitHub
3. أنشئ Issue جديد مع التفاصيل

---

**🎉 مبروك! نظام ويسترون لتحويل الأموال يعمل الآن بنجاح!**

للمزيد من التفاصيل، راجع [README.md](README.md) الكامل.
