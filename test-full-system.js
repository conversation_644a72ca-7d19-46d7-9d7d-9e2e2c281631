// Test Wistron Full System
const http = require('http');

console.log('🧪 Testing Wistron Money Transfer Full System...\n');

function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: method,
      headers: method === 'POST' ? {
        'Content-Type': 'application/json'
      } : {}
    };

    console.log(`Testing: ${method} ${path}`);

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        console.log(`✅ Status: ${res.statusCode}`);
        try {
          const parsed = JSON.parse(responseData);
          if (parsed.success) {
            console.log(`   Message: ${parsed.message || 'Success'}`);
            if (parsed.data) {
              console.log(`   Data: ${JSON.stringify(parsed.data).substring(0, 100)}...`);
            }
          } else {
            console.log(`   Error: ${parsed.error || parsed.message}`);
          }
        } catch (e) {
          console.log(`   Response: ${responseData.substring(0, 100)}...`);
        }
        console.log('');
        resolve({ status: res.statusCode, data: responseData });
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Request failed: ${err.message}\n`);
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function runFullSystemTests() {
  try {
    console.log('🏦 ========================================');
    console.log('   Wistron Money Transfer Full System Test');
    console.log('   اختبار نظام ويسترون الكامل');
    console.log('========================================\n');

    // Test 1: Health Check
    console.log('1. Testing System Health...');
    await testEndpoint('/health');

    // Test 2: Dashboard Stats
    console.log('2. Testing Dashboard Statistics...');
    await testEndpoint('/api/v1/dashboard/stats');

    // Test 3: Exchange Rates
    console.log('3. Testing Exchange Rates...');
    await testEndpoint('/api/v1/rates');

    // Test 4: User Login
    console.log('4. Testing User Authentication...');
    await testEndpoint('/api/v1/auth/login', 'POST', {
      email: '<EMAIL>',
      password: 'Admin@123456'
    });

    // Test 5: User Registration
    console.log('5. Testing User Registration...');
    await testEndpoint('/api/v1/auth/register', 'POST', {
      email: '<EMAIL>',
      password: 'Test@123456',
      firstName: 'Test',
      lastName: 'User',
      phone: '+**********',
      country: 'US'
    });

    // Test 6: Get Users
    console.log('6. Testing Users List...');
    await testEndpoint('/api/v1/users');

    // Test 7: Get Transfers
    console.log('7. Testing Transfers List...');
    await testEndpoint('/api/v1/transfers');

    // Test 8: Create Transfer
    console.log('8. Testing Transfer Creation...');
    await testEndpoint('/api/v1/transfers', 'POST', {
      senderName: 'John Doe',
      recipientName: 'Ahmed Ali',
      sendAmount: 1000,
      sendCurrency: 'USD',
      receiveCurrency: 'SAR',
      deliveryMethod: 'cash_pickup'
    });

    // Test 9: Get Updated Transfers
    console.log('9. Testing Updated Transfers List...');
    await testEndpoint('/api/v1/transfers');

    // Test 10: Updated Dashboard Stats
    console.log('10. Testing Updated Dashboard Statistics...');
    await testEndpoint('/api/v1/dashboard/stats');

    console.log('🎉 ========================================');
    console.log('   FULL SYSTEM TEST COMPLETED!');
    console.log('   اكتمل اختبار النظام الكامل!');
    console.log('========================================');
    console.log('✅ All endpoints tested successfully!');
    console.log('');
    console.log('🌐 System Access Points:');
    console.log('   Main Dashboard: http://localhost:5000');
    console.log('   API Documentation: http://localhost:5000/api-docs');
    console.log('   Health Check: http://localhost:5000/health');
    console.log('');
    console.log('👤 Test Accounts:');
    console.log('   Admin: <EMAIL> / Admin@123456');
    console.log('   Agent: <EMAIL> / Agent@123456');
    console.log('');
    console.log('🚀 Wistron Money Transfer Full System is operational!');
    console.log('========================================');

  } catch (error) {
    console.log('❌ Test suite error:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Make sure the server is running: node full-system.js');
    console.log('2. Check if port 5000 is available');
    console.log('3. Verify Express is installed: npm install express cors');
  }
}

// Start testing
runFullSystemTests();
