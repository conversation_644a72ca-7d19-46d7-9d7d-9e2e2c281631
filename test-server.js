// Test if server is running
const http = require('http');

console.log('Testing Wistron Server...');

const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/health',
  method: 'GET',
  timeout: 5000
};

const req = http.request(options, (res) => {
  console.log(`✅ Server is running! Status: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('✅ Health check response:', response);
      
      // Test login
      testLogin();
    } catch (e) {
      console.log('Response:', data);
    }
  });
});

req.on('error', (err) => {
  console.log('❌ Server is not running:', err.message);
  console.log('Please start the server first with: node simple-server.js');
});

req.on('timeout', () => {
  console.log('❌ Request timeout - server may not be responding');
  req.destroy();
});

req.end();

function testLogin() {
  console.log('\nTesting login...');
  
  const loginData = JSON.stringify({
    email: '<EMAIL>',
    password: 'Admin@123456'
  });
  
  const loginOptions = {
    hostname: 'localhost',
    port: 5000,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(loginData)
    }
  };
  
  const loginReq = http.request(loginOptions, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        if (response.success) {
          console.log('✅ Login successful!');
          console.log('User:', response.data.user.email);
          showSummary();
        } else {
          console.log('❌ Login failed:', response.error);
        }
      } catch (e) {
        console.log('Login response:', data);
      }
    });
  });
  
  loginReq.on('error', (err) => {
    console.log('❌ Login test failed:', err.message);
  });
  
  loginReq.write(loginData);
  loginReq.end();
}

function showSummary() {
  console.log(`
🎉 ========================================
   Wistron Money Transfer System
   نظام ويسترون لتحويل الأموال
========================================
✅ النظام يعمل بنجاح!

🌐 الروابط المتاحة:
   الصفحة الرئيسية: http://localhost:5000
   فحص الصحة: http://localhost:5000/health
   أسعار الصرف: http://localhost:5000/api/v1/rates

👤 حسابات التجربة:
   المدير: <EMAIL> / Admin@123456
   الوكيل: <EMAIL> / Agent@123456

🔧 لاختبار API:
   curl http://localhost:5000/health
   curl -X POST http://localhost:5000/api/v1/auth/login \\
     -H "Content-Type: application/json" \\
     -d '{"email":"<EMAIL>","password":"Admin@123456"}'

========================================`);
}
