version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: wistron_postgres
    environment:
      POSTGRES_DB: wistron_money_transfer
      POSTGRES_USER: wistron_user
      POSTGRES_PASSWORD: wistron_secure_password_2024
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - wistron_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U wistron_user -d wistron_money_transfer"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: wistron_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wistron_network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass wistron_redis_password_2024
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: wistron_backend
    environment:
      NODE_ENV: development
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: wistron_money_transfer
      DB_USER: wistron_user
      DB_PASSWORD: wistron_secure_password_2024
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: wistron_redis_password_2024
      JWT_SECRET: wistron_jwt_super_secret_key_2024_very_long_and_secure
      JWT_REFRESH_SECRET: wistron_refresh_jwt_super_secret_key_2024_very_long_and_secure
      ENCRYPTION_KEY: wistron_encryption_key_32_chars_long_2024
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - wistron_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: npm run dev

  # Frontend Web App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: wistron_frontend
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:5000/api
      NEXT_PUBLIC_APP_NAME: Wistron Money Transfer
      NEXT_PUBLIC_APP_VERSION: 1.0.0
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - wistron_network
    depends_on:
      - backend
    restart: unless-stopped
    command: npm run dev

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: wistron_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - wistron_network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: wistron_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - wistron_network
    restart: unless-stopped

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: wistron_grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: wistron_grafana_admin_2024
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - wistron_network
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  wistron_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
